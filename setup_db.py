#!/usr/bin/env python3
"""
Database setup script for the customer chatbot API.
"""
import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import settings
from app.database.connection import create_tables, drop_tables

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_database():
    """Set up the database with all required tables."""
    try:
        logger.info("Setting up database...")
        logger.info(f"Database URL: {settings.DATABASE_URL}")
        
        # Create tables
        create_tables()
        logger.info("✅ Database setup completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error setting up database: {e}")
        sys.exit(1)


def reset_database():
    """Reset the database by dropping and recreating all tables."""
    try:
        logger.info("Resetting database...")
        logger.info(f"Database URL: {settings.DATABASE_URL}")
        
        # Drop and recreate tables
        drop_tables()
        create_tables()
        logger.info("✅ Database reset completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error resetting database: {e}")
        sys.exit(1)


def check_database():
    """Check database connection and configuration."""
    try:
        from app.database.connection import engine
        
        logger.info("Checking database connection...")
        logger.info(f"Database URL: {settings.DATABASE_URL}")
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            logger.info("✅ Database connection successful!")
            
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Database setup script")
    parser.add_argument(
        "action",
        choices=["setup", "reset", "check"],
        help="Action to perform: setup (create tables), reset (drop and recreate), check (test connection)"
    )
    
    args = parser.parse_args()
    
    if args.action == "setup":
        setup_database()
    elif args.action == "reset":
        reset_database()
    elif args.action == "check":
        check_database()
