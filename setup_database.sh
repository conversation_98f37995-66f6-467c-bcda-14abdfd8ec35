#!/bin/bash

# Database setup script for chatbot application

echo "🗄️ Setting up PostgreSQL database for chatbot..."

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed. Please install it first:"
    echo "   Ubuntu/Debian: sudo apt install postgresql postgresql-contrib"
    echo "   macOS: brew install postgresql"
    exit 1
fi

# Check if PostgreSQL service is running
if ! sudo systemctl is-active --quiet postgresql; then
    echo "🔄 Starting PostgreSQL service..."
    sudo systemctl start postgresql
fi

echo "📝 Creating database and user..."

# Create database and user
sudo -u postgres psql << EOF
-- Create database
CREATE DATABASE chatbot_db;

-- Create user
CREATE USER chatbot_user WITH PASSWORD 'chatbot_secure_2024';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE chatbot_db TO chatbot_user;

-- Grant schema privileges
\c chatbot_db
GRANT USAGE ON SCHEMA public TO chatbot_user;
GRANT CREATE ON SCHEMA public TO chatbot_user;

-- Show created database and user
\l chatbot_db
\du chatbot_user

EOF

echo "✅ Database setup completed!"
echo ""
echo "📋 pgAdmin Connection Details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   Database: chatbot_db"
echo "   Username: chatbot_user"
echo "   Password: chatbot_secure_2024"
echo ""
echo "🔧 Next steps:"
echo "1. Update your .env file with the password: chatbot_secure_2024"
echo "2. Run: uv run python init_db.py"
echo "3. Test: uv run python test_database.py"
