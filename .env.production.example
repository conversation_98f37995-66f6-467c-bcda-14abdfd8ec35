# Production Environment Configuration
# Copy this file to .env for production deployment

# Google Gemini API Key (Required)
GEMINI_API_KEY=your_production_api_key_here

# Database Configuration (Required)
# Use a secure PostgreSQL connection string for production
DATABASE_URL=************************************************/your-db-name

# Environment Configuration
ENVIRONMENT=production

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Security Notes for Production:
# 1. Use strong, unique passwords for database
# 2. Enable SSL/TLS for database connections
# 3. Use environment variables or secrets management
# 4. Restrict database access to application servers only
# 5. Enable database connection pooling
# 6. Set up database backups and monitoring
# 7. Use a reverse proxy (nginx) in front of the application
# 8. Enable HTTPS with proper SSL certificates

# Example production database URLs:
# AWS RDS: postgresql://username:<EMAIL>:5432/dbname
# Google Cloud SQL: *****************************************************/dbname
# Azure Database: postgresql://username:<EMAIL>:5432/dbname
# Heroku Postgres: postgres://username:password@hostname:port/dbname
