#!/usr/bin/env python3
"""
Data management utility for the chatbot application.
"""
import os
import json
import argparse
import logging
from app.services.link_extractor import extract_all_links
from app.services.data_ingestion import fetch_and_process_data, generate_embeddings
from app.services.embedding_service import EmbeddingService
from app.database import test_connection, create_tables

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def show_status():
    """Show current data status."""
    logger.info("📊 Current Data Status:")
    logger.info("=" * 50)

    # Show file status
    files = {
        "data/sources.json": "Input sources (your links)",
        "data/result.json": "Extracted links",
        "data/data.json": "Merged data (used for processing)"
    }

    for file_path, description in files.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                logger.info(f"✅ {file_path}: {len(data)} entries - {description}")
            except Exception as e:
                logger.info(f"⚠️  {file_path}: exists but error reading - {description}")
        else:
            logger.info(f"❌ {file_path}: not found - {description}")

    # Show database status
    logger.info("\n💾 Database Status:")
    try:
        if test_connection():
            stats = EmbeddingService.get_embedding_stats()
            logger.info(f"✅ Database connected")
            logger.info(f"📊 Documents in database: {stats['total_documents']}")
            logger.info(f"🔢 Embeddings in database: {stats['total_embeddings']}")
            logger.info(f"🤖 Models used: {stats['models_used']}")
            logger.info(f"🚀 Ready for chatbot: {stats['has_embeddings']}")
        else:
            logger.info("❌ Database connection failed")
    except Exception as e:
        logger.info(f"⚠️  Database error: {e}")


def add_source(title, url):
    """Add a new source to sources.json."""
    sources_file = "data/sources.json"
    
    # Load existing sources
    if os.path.exists(sources_file):
        with open(sources_file, 'r') as f:
            sources = json.load(f)
    else:
        sources = []
    
    # Check if URL already exists
    for source in sources:
        if source['link'] == url:
            logger.warning(f"⚠️  URL already exists: {url}")
            return False
    
    # Add new source
    new_source = {"title": title, "link": url}
    sources.append(new_source)
    
    # Save updated sources
    with open(sources_file, 'w') as f:
        json.dump(sources, f, indent=2)
    
    logger.info(f"✅ Added new source: {title} -> {url}")
    logger.info(f"📊 Total sources: {len(sources)}")
    return True


def regenerate_all():
    """Regenerate all data from sources and save to database."""
    logger.info("🔄 Regenerating all data...")

    try:
        # Check database connection
        if not test_connection():
            logger.error("❌ Database connection failed!")
            return False

        create_tables()
        logger.info("✅ Database connection verified")

        # Extract links
        merged_file = extract_all_links()
        logger.info(f"✅ Link extraction completed: {merged_file}")

        # Process documents
        documents = fetch_and_process_data(merged_file)
        logger.info(f"✅ Processed {len(documents)} documents")

        # Generate embeddings
        embeddings = generate_embeddings(documents)
        logger.info(f"✅ Generated {len(embeddings)} embeddings")

        # Save to database
        logger.info("💾 Saving to database...")
        EmbeddingService.clear_all_documents()
        saved_count = EmbeddingService.batch_save_documents_with_embeddings(documents, embeddings)
        logger.info(f"✅ Saved {saved_count} documents with embeddings to database")

        logger.info("🎉 Data regeneration completed successfully!")
        return True

    except Exception as e:
        logger.error(f"❌ Error during regeneration: {e}")
        return False


def clean_data():
    """Clean generated data files."""
    files_to_clean = ["data/result.json", "data/data.json"]
    
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"🗑️  Removed: {file_path}")
        else:
            logger.info(f"⚠️  Not found: {file_path}")
    
    logger.info("✅ Cleanup completed")


def main():
    parser = argparse.ArgumentParser(description="Manage chatbot data")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Status command
    subparsers.add_parser('status', help='Show current data status')
    
    # Add source command
    add_parser = subparsers.add_parser('add', help='Add a new data source')
    add_parser.add_argument('title', help='Title for the source')
    add_parser.add_argument('url', help='URL of the source')
    
    # Regenerate command
    subparsers.add_parser('regenerate', help='Regenerate all data from sources')
    
    # Clean command
    subparsers.add_parser('clean', help='Clean generated data files')
    
    args = parser.parse_args()
    
    if args.command == 'status':
        show_status()
    elif args.command == 'add':
        if add_source(args.title, args.url):
            logger.info("💡 Run 'python manage_data.py regenerate' to update the chatbot data")
    elif args.command == 'regenerate':
        regenerate_all()
    elif args.command == 'clean':
        clean_data()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
