#!/usr/bin/env python3
"""
Test script to verify GitHub filtering functionality.
"""
from app.services.link_extractor import is_valid_github_link, filter_github_links

def test_github_filtering():
    """Test the GitHub filtering logic."""
    print("🧪 Testing GitHub URL Filtering")
    print("=" * 50)
    
    # Test URLs
    test_urls = [
        # Should be ACCEPTED (contain NoCaptcha keywords)
        "https://github.com/noCaptchaAi/NoCaptcha-Ai-Browser-Extension",
        "https://github.com/noCaptchaAi/api-docs",
        "https://github.com/user/nocaptcha-integration",
        "https://github.com/developer/nocaptch-solver",
        "https://github.com/company/NoCaptchaAI-SDK",
        
        # Should be REJECTED (GitHub but no NoCaptcha keywords)
        "https://github.com/microsoft/vscode",
        "https://github.com/facebook/react",
        "https://github.com/google/tensorflow",
        "https://github.com/random/captcha-solver",
        "https://github.com/other/recaptcha-bypass",
        
        # Should be ACCEPTED (non-GitHub URLs - always accepted)
        "https://docs.nocaptchaai.com/",
        "https://nocaptchaai.com/pricing",
        "https://example.com/random-page",
        "https://stackoverflow.com/questions/12345",
    ]
    
    print("Testing individual URLs:")
    print("-" * 30)
    
    accepted_count = 0
    rejected_count = 0
    
    for url in test_urls:
        is_valid = is_valid_github_link(url)
        status = "✅ ACCEPTED" if is_valid else "❌ REJECTED"
        
        # Determine expected result
        is_github = 'github.com' in url.lower()
        has_nocaptcha = any(keyword in url.lower() for keyword in ['nocaptchaai', 'nocaptch', 'nocaptcha'])
        expected = not is_github or has_nocaptcha
        
        result_check = "✅" if is_valid == expected else "⚠️ UNEXPECTED"
        
        print(f"{result_check} {status}: {url}")
        
        if is_valid:
            accepted_count += 1
        else:
            rejected_count += 1
    
    print(f"\nSummary:")
    print(f"✅ Accepted: {accepted_count}")
    print(f"❌ Rejected: {rejected_count}")
    print(f"📊 Total tested: {len(test_urls)}")
    
    # Test the filter_github_links function
    print("\n" + "=" * 50)
    print("Testing filter_github_links function:")
    print("-" * 30)
    
    test_sources = [
        {"title": "NoCaptcha Extension", "link": "https://github.com/noCaptchaAi/NoCaptcha-Ai-Browser-Extension"},
        {"title": "VS Code", "link": "https://github.com/microsoft/vscode"},
        {"title": "Documentation", "link": "https://docs.nocaptchaai.com/"},
        {"title": "React", "link": "https://github.com/facebook/react"},
        {"title": "NoCaptcha API", "link": "https://github.com/user/nocaptcha-integration"},
        {"title": "Random Captcha", "link": "https://github.com/random/captcha-solver"},
    ]
    
    print(f"Before filtering: {len(test_sources)} sources")
    for i, source in enumerate(test_sources, 1):
        print(f"  {i}. {source['title']}: {source['link']}")
    
    filtered_sources = filter_github_links(test_sources)
    
    print(f"\nAfter filtering: {len(filtered_sources)} sources")
    for i, source in enumerate(filtered_sources, 1):
        print(f"  {i}. {source['title']}: {source['link']}")
    
    # Expected results
    expected_filtered = [
        "https://github.com/noCaptchaAi/NoCaptcha-Ai-Browser-Extension",  # Has noCaptchaAi
        "https://docs.nocaptchaai.com/",  # Non-GitHub
        "https://github.com/user/nocaptcha-integration",  # Has nocaptcha
    ]
    
    filtered_links = [source['link'] for source in filtered_sources]
    
    print(f"\n🎯 Filtering Results:")
    print(f"Expected {len(expected_filtered)} sources to remain")
    print(f"Actually got {len(filtered_sources)} sources")
    
    if len(filtered_sources) == len(expected_filtered):
        print("✅ Correct number of sources filtered")
    else:
        print("❌ Unexpected number of sources")
    
    # Check if all expected URLs are present
    all_expected_present = all(url in filtered_links for url in expected_filtered)
    if all_expected_present:
        print("✅ All expected URLs are present")
    else:
        print("❌ Some expected URLs are missing")
        missing = [url for url in expected_filtered if url not in filtered_links]
        print(f"Missing: {missing}")
    
    print("\n🎉 GitHub filtering test completed!")


if __name__ == "__main__":
    test_github_filtering()
