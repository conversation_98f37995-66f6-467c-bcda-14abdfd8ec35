#!/usr/bin/env python3
"""
Test script to verify the 60% confidence threshold is working correctly.
"""
import asyncio
import requests
import json
from app.config import settings

def test_chat_endpoint(query: str, expected_approach: str = None):
    """Test a query against the chat endpoint."""
    url = f"http://127.0.0.1:{settings.PORT}/chat"
    
    payload = {
        "query": query
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            rag_used = data.get("rag_used", False)
            confidence_score = data.get("confidence_score")
            function_called = data.get("function_called")
            
            # Determine actual approach
            if rag_used:
                actual_approach = f"RAG ({confidence_score:.1%})" if confidence_score else "RAG"
            elif function_called:
                actual_approach = f"Tool ({function_called})"
            else:
                actual_approach = "Fallback"
            
            # Check if threshold logic is working
            threshold_check = "✅"
            if rag_used and confidence_score and confidence_score < 0.6:
                threshold_check = "❌ RAG used but confidence < 60%"
            elif not rag_used and confidence_score and confidence_score >= 0.6:
                threshold_check = "❌ Tool used but confidence >= 60%"
            
            print(f"Query: '{query}'")
            print(f"  Approach: {actual_approach}")
            print(f"  Threshold Check: {threshold_check}")
            print(f"  Response: {data.get('response', '')[:100]}...")
            print("-" * 60)
            
            return data
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None


def main():
    """Test various queries to verify 60% threshold."""
    print("🧪 Testing 60% Confidence Threshold")
    print("=" * 60)
    print(f"Configuration: RAG_CONFIDENCE_THRESHOLD = {settings.RAG_CONFIDENCE_THRESHOLD}")
    print(f"Expected behavior: RAG if confidence >= 60%, Tools if < 60%")
    print("=" * 60)
    
    # Test queries that should likely use RAG (high confidence)
    print("\n📚 Queries that should likely use RAG (>= 60% confidence):")
    rag_queries = [
        "What are your pricing plans?",
        "How do I integrate the NoCaptcha API?",
        "What captcha types are supported?",
        "How to use the browser extension?",
        "API documentation for developers"
    ]
    
    for query in rag_queries:
        test_chat_endpoint(query, "RAG")
    
    # Test queries that should likely use Tools (low confidence)
    print("\n🛠️ Queries that should likely use Tools (< 60% confidence):")
    tool_queries = [
        "I want a refund",
        "What payment methods do you accept?",
        "Contact support",
        "I need help with my account",
        "Captcha not working properly"
    ]
    
    for query in tool_queries:
        test_chat_endpoint(query, "Tools")
    
    # Test edge cases
    print("\n🎯 Edge case queries:")
    edge_queries = [
        "Hello",
        "Thank you",
        "How are you?",
        "What is NoCaptcha?"
    ]
    
    for query in edge_queries:
        test_chat_endpoint(query, "Unknown")
    
    print("\n✅ Testing completed!")
    print("Check the results above to verify the 60% threshold is working correctly.")


if __name__ == "__main__":
    main()
