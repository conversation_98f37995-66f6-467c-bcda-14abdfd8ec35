#!/usr/bin/env python3
"""
Simple run script for the NoCaptcha AI Customer Service Chatbot.
"""
import uvicorn
from app.config import settings

if __name__ == "__main__":
    print("🚀 Starting NoCaptcha AI Customer Service Chatbot...")
    print(f"📡 Server will be available at: http://{settings.HOST}:{settings.PORT}")
    print(f"🎨 Web interface: http://{settings.HOST}:{settings.PORT}/")
    print(f"📚 API docs: http://{settings.HOST}:{settings.PORT}/docs")
    print("=" * 60)
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
