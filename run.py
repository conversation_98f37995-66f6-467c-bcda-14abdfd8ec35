#!/usr/bin/env python3
"""
Simple script to run the customer chatbot API.
"""
import uvicorn
from app.config import settings

if __name__ == "__main__":
    print("🚀 Starting Customer Chatbot API...")
    print(f"📍 Server will run on: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API Documentation: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔧 Debug mode: {settings.DEBUG}")
    print()
    
    if not settings.GEMINI_API_KEY or settings.GEMINI_API_KEY == "your_api_key_here":
        print("⚠️  WARNING: GEMINI_API_KEY not set or using placeholder value!")
        print("   Please set your API key in the .env file")
        print("   Get your API key from: https://ai.google.dev/")
        print()
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
