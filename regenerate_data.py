#!/usr/bin/env python3
"""
Script to regenerate all data from sources.json and save embeddings to database
"""
import os
import logging
from app.services.link_extractor import extract_all_links
from app.services.data_ingestion import fetch_and_process_data, generate_embeddings
from app.services.embedding_service import EmbeddingService
from app.database import create_tables, test_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def regenerate_all_data():
    """Regenerate all data from sources.json and save to database"""
    logger.info("🔄 Starting complete data regeneration...")

    # Step 1: Check database connection
    logger.info("🔗 Checking database connection...")
    if not test_connection():
        logger.error("❌ Database connection failed!")
        logger.error("Please check your database configuration in .env file")
        return False

    # Ensure database tables exist
    create_tables()
    logger.info("✅ Database connection verified")

    # Step 2: Check if sources.json exists
    sources_file = "data/sources.json"
    if not os.path.exists(sources_file):
        logger.error(f"❌ Sources file not found: {sources_file}")
        logger.error("Please make sure data/sources.json exists with your data sources")
        return False

    # Step 3: Extract all links from sources with GitHub filtering
    logger.info("📡 Extracting links from all sources...")
    logger.info("🔍 GitHub filtering: Only GitHub URLs containing 'noCaptchaAi', 'noCaptch', or 'nocaptcha' will be included")
    try:
        merged_file = extract_all_links(
            sources_file="data/sources.json",
            output_file="data/result.json",
            merged_file="data/data.json"
        )
        logger.info(f"✅ Link extraction and GitHub filtering completed: {merged_file}")
    except Exception as e:
        logger.error(f"❌ Error during link extraction: {e}")
        return False

    # Step 4: Process all documents and generate embeddings
    logger.info("📚 Processing documents and generating embeddings...")
    try:
        documents = fetch_and_process_data("data/data.json")
        if not documents:
            logger.error("❌ No documents were processed")
            return False

        logger.info(f"✅ Processed {len(documents)} documents")

        embeddings = generate_embeddings(documents)
        logger.info(f"✅ Generated {len(embeddings)} embeddings")

        # Step 5: Clear existing embeddings and save new ones to database
        logger.info("💾 Saving embeddings to database...")

        # Clear existing data
        if EmbeddingService.clear_all_documents():
            logger.info("✅ Cleared existing documents from database")

        # Save new documents and embeddings
        saved_count = EmbeddingService.batch_save_documents_with_embeddings(
            documents, embeddings
        )

        if saved_count == len(documents):
            logger.info(f"✅ Saved {saved_count} documents with embeddings to database")
        else:
            logger.warning(f"⚠️  Only saved {saved_count}/{len(documents)} documents")

    except Exception as e:
        logger.error(f"❌ Error during document processing: {e}")
        return False

    logger.info("🎉 Data regeneration completed successfully!")
    logger.info(f"📊 Total documents processed: {len(documents)}")
    logger.info(f"💾 Documents saved to database: {saved_count}")
    logger.info("🚀 Your chatbot is ready with the latest data!")

    return True


def show_data_summary():
    """Show summary of current data files and database"""
    logger.info("📋 Data Files Summary:")

    files_to_check = [
        "data/sources.json",
        "data/result.json",
        "data/data.json"
    ]

    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                import json
                with open(file_path, 'r') as f:
                    data = json.load(f)
                logger.info(f"✅ {file_path}: {len(data)} entries")
            except Exception as e:
                logger.info(f"⚠️  {file_path}: exists but error reading ({e})")
        else:
            logger.info(f"❌ {file_path}: not found")

    # Show database stats
    logger.info("\n💾 Database Summary:")
    try:
        stats = EmbeddingService.get_embedding_stats()
        logger.info(f"📊 Documents in database: {stats['total_documents']}")
        logger.info(f"🔢 Embeddings in database: {stats['total_embeddings']}")
        logger.info(f"🤖 Models used: {stats['models_used']}")
        logger.info(f"✅ Has embeddings: {stats['has_embeddings']}")
    except Exception as e:
        logger.info(f"⚠️  Database stats error: {e}")


if __name__ == "__main__":
    logger.info("🔄 Data Regeneration Tool")
    logger.info("=" * 50)
    
    # Show current state
    show_data_summary()
    logger.info("")
    
    # Regenerate everything
    success = regenerate_all_data()
    
    logger.info("")
    logger.info("=" * 50)
    
    if success:
        logger.info("✅ SUCCESS: Data regeneration completed!")
        show_data_summary()
    else:
        logger.error("❌ FAILED: Data regeneration failed!")
        exit(1)
