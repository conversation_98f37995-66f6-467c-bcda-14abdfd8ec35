# Database Setup Guide

This guide explains how to set up PostgreSQL database for the Customer Chatbot API.

## 🗄️ Database Features

- **PostgreSQL** database for storing conversations and messages
- **Unique message IDs** using UUIDs for each query and response
- **Conversation management** with session tracking
- **User-based conversation history**
- **Source tracking** for RAG responses
- **Proper indexing** for performance

## 🚀 Quick Setup with Docker

### 1. Start PostgreSQL with Docker Compose

```bash
# Start PostgreSQL database
docker-compose up -d postgres

# Optional: Start with pgAdmin for database management
docker-compose --profile admin up -d
```

### 2. Install Dependencies

```bash
# Install new dependencies
uv sync
```

### 3. Set Up Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env and configure your database
# The default settings work with the Docker setup
```

### 4. Initialize Database

```bash
# Create database tables
python setup_db.py setup

# Or check connection
python setup_db.py check
```

## 🔧 Manual PostgreSQL Setup

If you prefer to install PostgreSQL manually:

### 1. Install PostgreSQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS with Homebrew
brew install postgresql
brew services start postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

### 2. Create Database and User

```sql
-- Connect to PostgreSQL as superuser
sudo -u postgres psql

-- Create database and user
CREATE DATABASE chatbot_db;
CREATE USER chatbot_user WITH PASSWORD 'chatbot_password';
GRANT ALL PRIVILEGES ON DATABASE chatbot_db TO chatbot_user;

-- Enable UUID extension
\c chatbot_db
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Exit
\q
```

### 3. Update Environment Variables

```bash
# In your .env file
DATABASE_URL=postgresql://chatbot_user:chatbot_password@localhost:5432/chatbot_db
```

## 📊 Database Schema

### Conversations Table
- `id` (UUID, Primary Key)
- `session_id` (String, Optional) - For grouping related conversations
- `user_id` (String, Optional) - For user-specific conversations
- `title` (String, Optional) - Auto-generated from first query
- `created_at` (DateTime)
- `updated_at` (DateTime)

### Messages Table
- `id` (UUID, Primary Key)
- `conversation_id` (UUID, Foreign Key)
- `message_type` (String) - 'query' or 'response'
- `content` (Text) - The actual message content
- `sources` (JSON, Optional) - Sources used for responses
- `relevance_scores` (JSON, Optional) - Relevance scores
- `model_used` (String, Optional) - AI model used
- `temperature` (Float, Optional) - Generation temperature
- `max_tokens` (Integer, Optional) - Max tokens used
- `created_at` (DateTime)

## 🔄 Database Migrations

### Using Alembic (Recommended)

```bash
# Generate initial migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head

# Check current revision
alembic current

# Downgrade if needed
alembic downgrade -1
```

### Using Setup Script

```bash
# Create tables
python setup_db.py setup

# Reset database (drops and recreates all tables)
python setup_db.py reset

# Check connection
python setup_db.py check
```

## 🌍 Environment Configuration

### Development (.env)
```bash
ENVIRONMENT=development
DATABASE_URL=postgresql://chatbot_user:chatbot_password@localhost:5432/chatbot_db
DEBUG=true
```

### Production (.env)
```bash
ENVIRONMENT=production
DATABASE_URL=********************************************************/your-db-name
DEBUG=false
```

## 📡 API Endpoints

### Chat Endpoint (Enhanced)
```bash
POST /chat
{
  "query": "What is FastAPI?",
  "session_id": "optional-session-id",
  "user_id": "optional-user-id",
  "conversation_id": "optional-existing-conversation-uuid",
  "max_tokens": 1000,
  "temperature": 0.7
}
```

Response includes:
- `message_id` - Unique ID for the response
- `conversation_id` - ID of the conversation
- `query_message_id` - ID of the original query
- `created_at` - Timestamp

### Conversation Management
```bash
# Get conversation with messages
GET /conversations/{conversation_id}

# Get user's conversations
GET /conversations?user_id=user123&limit=20

# Get messages for a conversation
GET /conversations/{conversation_id}/messages?limit=50
```

## 🔍 Monitoring and Maintenance

### Database Health Check
```bash
# Check database connection
python setup_db.py check

# Or use the API health endpoint
curl http://localhost:8000/health
```

### Performance Monitoring
- Monitor query performance with PostgreSQL logs
- Use pgAdmin for database administration
- Consider connection pooling for production

### Backup and Recovery
```bash
# Backup database
pg_dump -U chatbot_user -h localhost chatbot_db > backup.sql

# Restore database
psql -U chatbot_user -h localhost chatbot_db < backup.sql
```

## 🚨 Troubleshooting

### Common Issues

1. **Connection refused**
   - Check if PostgreSQL is running
   - Verify connection string in .env
   - Check firewall settings

2. **Permission denied**
   - Verify database user permissions
   - Check if user can connect to the database

3. **Table doesn't exist**
   - Run `python setup_db.py setup`
   - Or use Alembic migrations

4. **UUID extension missing**
   - Connect to database and run: `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`

### Debug Mode
Set `DEBUG=true` in your .env file to see SQL queries in the logs.

## 🔐 Security Considerations

- Use strong passwords for database users
- Limit database user permissions in production
- Use SSL connections for production databases
- Regularly update PostgreSQL and dependencies
- Consider using connection pooling and read replicas for high traffic
