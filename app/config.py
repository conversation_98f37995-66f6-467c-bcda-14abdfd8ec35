"""
Configuration settings for the RAG chatbot API.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    """Application settings."""
    
    # API Configuration
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    
    # Model Configuration
    EMBEDDING_MODEL: str = os.getenv("EMBEDDING_MODEL", "models/text-embedding-004")
    GENERATION_MODEL: str = os.getenv("GENERATION_MODEL", "models/gemini-2.0-flash-exp")

    # RAG Configuration
    RAG_CONFIDENCE_THRESHOLD: float = float(os.getenv("RAG_CONFIDENCE_THRESHOLD", "0.6"))  # 60% threshold
    RAG_FALLBACK_THRESHOLD: float = float(os.getenv("RAG_FALLBACK_THRESHOLD", "0.6"))     # Same as main threshold
    TOP_K_RESULTS: int = int(os.getenv("TOP_K_RESULTS", "5"))
    MAX_CONTEXT_LENGTH: int = int(os.getenv("MAX_CONTEXT_LENGTH", "4000"))

    # Data Configuration
    DATA_SOURCES_PATH: str = os.getenv("DATA_SOURCES_PATH", "data/data.json")

    # Server Configuration
    HOST: str = os.getenv("HOST", "127.0.0.1")
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"

    # Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://chatbot_user:chatbot_secure_2024@localhost:5432/chatbot_db")
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: int = int(os.getenv("DB_PORT", "5432"))
    DB_NAME: str = os.getenv("DB_NAME", "chatbot_db")
    DB_USER: str = os.getenv("DB_USER", "chatbot_user")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "chatbot_secure_2024")

    # Company Information
    COMPANY_NAME: str = os.getenv("COMPANY_NAME", "NoCaptcha AI")
    COMPANY_DOMAIN: str = os.getenv("COMPANY_DOMAIN", "nocaptchaai.com")
    SUPPORT_EMAIL: str = os.getenv("SUPPORT_EMAIL", "<EMAIL>")

    # System Prompts
    RAG_SYSTEM_PROMPT: str = os.getenv("RAG_SYSTEM_PROMPT", """You are a helpful customer service assistant for NoCaptcha AI. Answer the user's question based on the documentation provided below.

        Instructions:
        - Answer directly and helpfully based on the documentation
        - If the documentation contains pricing information, provide specific details
        - If the documentation contains service information, be comprehensive
        - Be conversational and professional
        - If you can answer from the documentation, do NOT suggest contacting support""")

    TOOL_CALLING_PROMPT: str = os.getenv("TOOL_CALLING_PROMPT", """
                                         You are a helpful customer service assistant for NoCaptcha AI. I couldn't find specific information in our knowledge base for this query. Please help by calling the appropriate function to get the user the information they need.""")

    def validate(self) -> bool:
        """Validate that required settings are present."""
        if not self.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        return True

# Global settings instance
settings = Settings()
