"""
Configuration settings for the RAG chatbot API.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    """Application settings."""
    
    # API Configuration
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    
    # Model Configuration
    EMBEDDING_MODEL: str = "models/text-embedding-004"
    GENERATION_MODEL: str = "gemini-2.0-flash-exp"
    
    # Retrieval Configuration
    TOP_K_RESULTS: int = 5
    MAX_CONTEXT_LENGTH: int = 4000
    
    # Data Configuration
    DATA_SOURCES_PATH: str = "data/data.json"
    
    # Server Configuration
    HOST: str = os.getenv("HOST", "127.0.0.1")
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    def validate(self) -> bool:
        """Validate that required settings are present."""
        if not self.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        return True

# Global settings instance
settings = Settings()
