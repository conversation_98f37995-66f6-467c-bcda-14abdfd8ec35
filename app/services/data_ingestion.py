"""
Data ingestion and embedding generation service.
"""
import requests
from bs4 import BeautifulSoup
import json
import os
import logging
from typing import List, Dict, Any
from google import genai
from google.genai import types
from app.config import settings
from app.services.link_extractor import extract_all_links

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure the Gen AI client
client = genai.Client(api_key=settings.GEMINI_API_KEY)

def fetch_and_process_data(json_path: str) -> List[Dict[str, Any]]:
    """
    Fetch data from sources defined in JSON file and process them.
    If data.json exists, use it directly. Otherwise, extract links from sources.json first.

    Args:
        json_path: Path to the JSON file containing data sources

    Returns:
        List of processed documents with title and content
    """
    try:
        # Check if we're using data.json directly or need to extract from sources.json
        if json_path == "data/data.json" and os.path.exists(json_path):
            # Use data.json directly (already contains extracted links)
            logger.info("Using existing data.json file with extracted links...")
            with open(json_path, 'r', encoding='utf-8') as f:
                sources = json.load(f)
            logger.info(f"Loaded {len(sources)} sources from data.json")
        else:
            # Extract links from sources.json first
            logger.info("Starting link extraction from sources.json...")
            merged_file = extract_all_links(
                sources_file="data/sources.json",
                output_file="data/result.json",
                merged_file="data/data.json"
            )

            # Load the merged sources (original + extracted links)
            with open(merged_file, 'r', encoding='utf-8') as f:
                sources = json.load(f)

            logger.info(f"Using merged sources file with {len(sources)} total sources")

    except FileNotFoundError:
        logger.error(f"Sources file not found: {json_path}")
        return []
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in sources file: {json_path}")
        return []
    except Exception as e:
        logger.error(f"Error during link extraction: {e}")
        # Fallback to original sources file
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                sources = json.load(f)
            logger.info("Falling back to original sources file")
        except Exception:
            logger.error("Could not load original sources file either")
            return []
    
    documents = []
    for source in sources:
        try:
            logger.info(f"Fetching data from: {source.get('title', 'Unknown')}")
            response = requests.get(source['link'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Limit text length to avoid token limits
            if len(text) > settings.MAX_CONTEXT_LENGTH:
                text = text[:settings.MAX_CONTEXT_LENGTH]
            
            documents.append({
                'title': source['title'],
                'content': text,
                'url': source['link']
            })
            
        except requests.RequestException as e:
            logger.error(f"Error fetching {source.get('link', 'unknown URL')}: {e}")
        except Exception as e:
            logger.error(f"Error processing {source.get('title', 'unknown source')}: {e}")
    
    logger.info(f"Successfully processed {len(documents)} documents")
    return documents

def generate_embeddings(documents: List[Dict[str, Any]]) -> List[List[float]]:
    """
    Generate embeddings for a list of documents.
    
    Args:
        documents: List of documents with content to embed
        
    Returns:
        List of embedding vectors
    """
    texts = [doc['content'] for doc in documents]
    embeddings = []

    for i, text in enumerate(texts):
        try:
            logger.info(f"Generating embedding for document {i+1}/{len(texts)}")
            response = client.models.embed_content(
                model=settings.EMBEDDING_MODEL,
                contents=text,
                config=types.EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT")
            )
            # Handle different response structures
            if hasattr(response, 'embeddings') and response.embeddings:
                embeddings.append(response.embeddings[0].values)
            elif hasattr(response, 'embedding'):
                embeddings.append(response.embedding)
            else:
                logger.error(f"Unexpected response structure: {response}")
                embeddings.append([0.0] * 768)
        except Exception as e:
            logger.error(f"Error generating embedding for document {i}: {e}")
            embeddings.append([0.0] * 768)  # Adjust dimension as per model
    logger.info(f"Generated {len(embeddings)} embeddings")
    return embeddings
def generate_query_embedding(query: str) -> List[float]:
    """
    Generate embedding for a query.
    
    Args:
        query: Query text to embed
        
    Returns:
        Query embedding vector
    """
    try:
        response = client.models.embed_content(
            model=settings.EMBEDDING_MODEL,
            contents=query,
            config=types.EmbedContentConfig(task_type="RETRIEVAL_QUERY")
        )
        # Handle different response structures
        if hasattr(response, 'embeddings') and response.embeddings:
            return response.embeddings[0].values
        elif hasattr(response, 'embedding'):
            return response.embedding
        else:
            logger.error(f"Unexpected response structure: {response}")
            raise ValueError("Could not extract embedding from response")
    except Exception as e:
        logger.error(f"Error generating query embedding: {e}")
        raise
