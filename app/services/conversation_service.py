"""
Service for handling conversation and message operations.
"""
import uuid
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.conversation import Conversation, Message
from app.schemas.conversation import ConversationCreate, MessageCreate

logger = logging.getLogger(__name__)


class ConversationService:
    """Service for managing conversations and messages."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_conversation(
        self, 
        conversation_data: ConversationCreate
    ) -> Conversation:
        """
        Create a new conversation.
        
        Args:
            conversation_data: Data for creating the conversation
            
        Returns:
            Created conversation
        """
        conversation = Conversation(
            session_id=conversation_data.session_id,
            user_id=conversation_data.user_id,
            title=conversation_data.title
        )
        
        self.db.add(conversation)
        self.db.commit()
        self.db.refresh(conversation)
        
        logger.info(f"Created conversation {conversation.id}")
        return conversation
    
    def get_conversation(self, conversation_id: uuid.UUID) -> Optional[Conversation]:
        """
        Get a conversation by ID.
        
        Args:
            conversation_id: UUID of the conversation
            
        Returns:
            Conversation if found, None otherwise
        """
        return self.db.query(Conversation).filter(
            Conversation.id == conversation_id
        ).first()
    
    def get_or_create_conversation(
        self,
        conversation_id: Optional[uuid.UUID] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Conversation:
        """
        Get existing conversation or create a new one.
        
        Args:
            conversation_id: Existing conversation ID
            session_id: Session ID for grouping messages
            user_id: User ID
            
        Returns:
            Conversation instance
        """
        if conversation_id:
            conversation = self.get_conversation(conversation_id)
            if conversation:
                return conversation
        
        # Create new conversation
        conversation_data = ConversationCreate(
            session_id=session_id,
            user_id=user_id,
            title=None  # Will be set based on first query
        )
        return self.create_conversation(conversation_data)
    
    def add_message(
        self,
        conversation_id: uuid.UUID,
        content: str,
        message_type: str,
        sources: Optional[List[Dict[str, Any]]] = None,
        relevance_scores: Optional[List[float]] = None,
        model_used: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> Message:
        """
        Add a message to a conversation.
        
        Args:
            conversation_id: ID of the conversation
            content: Message content
            message_type: Type of message ('query' or 'response')
            sources: Sources used for the response
            relevance_scores: Relevance scores for sources
            model_used: Model used for generation
            temperature: Temperature used for generation
            max_tokens: Max tokens used
            
        Returns:
            Created message
        """
        message = Message(
            conversation_id=conversation_id,
            content=content,
            message_type=message_type,
            sources=sources,
            relevance_scores=relevance_scores,
            model_used=model_used,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        self.db.add(message)
        self.db.commit()
        self.db.refresh(message)
        
        # Update conversation's updated_at timestamp
        conversation = self.get_conversation(conversation_id)
        if conversation:
            conversation.updated_at = datetime.utcnow()
            self.db.commit()
        
        logger.info(f"Added {message_type} message {message.id} to conversation {conversation_id}")
        return message
    
    def get_conversation_messages(
        self, 
        conversation_id: uuid.UUID,
        limit: Optional[int] = None
    ) -> List[Message]:
        """
        Get messages for a conversation.
        
        Args:
            conversation_id: ID of the conversation
            limit: Maximum number of messages to return
            
        Returns:
            List of messages ordered by creation time
        """
        query = self.db.query(Message).filter(
            Message.conversation_id == conversation_id
        ).order_by(Message.created_at)
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def get_user_conversations(
        self,
        user_id: str,
        limit: Optional[int] = 20
    ) -> List[Conversation]:
        """
        Get conversations for a user.
        
        Args:
            user_id: User ID
            limit: Maximum number of conversations to return
            
        Returns:
            List of conversations ordered by update time (most recent first)
        """
        query = self.db.query(Conversation).filter(
            Conversation.user_id == user_id
        ).order_by(desc(Conversation.updated_at))
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def update_conversation_title(
        self,
        conversation_id: uuid.UUID,
        title: str
    ) -> Optional[Conversation]:
        """
        Update conversation title.
        
        Args:
            conversation_id: ID of the conversation
            title: New title
            
        Returns:
            Updated conversation if found, None otherwise
        """
        conversation = self.get_conversation(conversation_id)
        if conversation:
            conversation.title = title
            conversation.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(conversation)
            logger.info(f"Updated title for conversation {conversation_id}")
        
        return conversation
