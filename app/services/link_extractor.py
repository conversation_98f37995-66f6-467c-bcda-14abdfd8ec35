"""
Link extraction service for crawling and extracting links from websites.
"""
import requests
from bs4 import BeautifulSoup
import json
import logging
from urllib.parse import urljoin, urlparse
from typing import List, Dict, Set
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LinkExtractor:
    """Extract links from websites and create comprehensive source lists."""
    
    def __init__(self, max_depth: int = 2, delay: float = 1.0, max_links_per_site: int = 50):
        self.max_depth = max_depth
        self.delay = delay  # Delay between requests to be respectful
        self.max_links_per_site = max_links_per_site
        self.visited_urls: Set[str] = set()
        
    def is_valid_url(self, url: str, base_domain: str) -> bool:
        """Check if URL is valid and belongs to the same domain."""
        try:
            parsed = urlparse(url)
            base_parsed = urlparse(base_domain)
            
            # Must be same domain
            if parsed.netloc != base_parsed.netloc:
                return False
                
            # Skip certain file types
            skip_extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.gif', '.zip', '.exe', '.dmg'}
            if any(url.lower().endswith(ext) for ext in skip_extensions):
                return False
                
            # Skip certain paths
            skip_paths = {'#', 'javascript:', 'mailto:', 'tel:', 'ftp:'}
            if any(url.lower().startswith(path) for path in skip_paths):
                return False
                
            return True
        except Exception:
            return False
    
    def extract_links_from_page(self, url: str, base_domain: str) -> List[str]:
        """Extract all valid links from a single page."""
        try:
            logger.info(f"Extracting links from: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            links = []
            
            # Find all anchor tags with href
            for link in soup.find_all('a', href=True):
                href = link['href']
                
                # Convert relative URLs to absolute
                absolute_url = urljoin(url, href)
                
                # Clean URL (remove fragments)
                clean_url = absolute_url.split('#')[0]
                
                if self.is_valid_url(clean_url, base_domain) and clean_url not in self.visited_urls:
                    links.append(clean_url)
                    self.visited_urls.add(clean_url)
            
            logger.info(f"Found {len(links)} valid links on {url}")
            return links
            
        except Exception as e:
            logger.error(f"Error extracting links from {url}: {e}")
            return []
    
    def crawl_website(self, base_url: str, title: str) -> List[Dict[str, str]]:
        """Crawl a website and extract all links up to max_depth."""
        logger.info(f"Starting crawl of {base_url} with max_depth={self.max_depth}")
        
        all_sources = []
        urls_to_process = [(base_url, 0)]  # (url, depth)
        processed_count = 0
        
        # Add the base URL first
        all_sources.append({
            "title": title,
            "link": base_url
        })
        self.visited_urls.add(base_url)
        
        while urls_to_process and processed_count < self.max_links_per_site:
            current_url, depth = urls_to_process.pop(0)
            
            if depth >= self.max_depth:
                continue
                
            # Extract links from current page
            links = self.extract_links_from_page(current_url, base_url)
            
            for link in links:
                if processed_count >= self.max_links_per_site:
                    break
                    
                # Add to sources
                link_title = f"{title} - Page {processed_count + 1}"
                all_sources.append({
                    "title": link_title,
                    "link": link
                })
                
                # Add to queue for further processing if within depth limit
                if depth + 1 < self.max_depth:
                    urls_to_process.append((link, depth + 1))
                
                processed_count += 1
            
            # Be respectful - add delay between requests
            time.sleep(self.delay)
        
        logger.info(f"Crawl completed. Found {len(all_sources)} total sources for {base_url}")
        return all_sources

def extract_all_links(sources_file: str = "data/sources.json",
                     output_file: str = "data/result.json",
                     merged_file: str = "data/data.json") -> str:
    """
    Extract links from all sources and create merged file.
    
    Args:
        sources_file: Input sources JSON file
        output_file: Output file for extracted links
        merged_file: Final merged file to use for embeddings
        
    Returns:
        Path to the merged file
    """
    try:
        # Load original sources
        with open(sources_file, 'r', encoding='utf-8') as f:
            original_sources = json.load(f)
        
        logger.info(f"Loaded {len(original_sources)} original sources")
        
        # Initialize link extractor
        extractor = LinkExtractor(max_depth=2, delay=1.0, max_links_per_site=30)
        
        all_extracted_sources = []
        
        # Extract links from each source
        for source in original_sources:
            try:
                extracted = extractor.crawl_website(source['link'], source['title'])
                all_extracted_sources.extend(extracted)
                
                # Reset visited URLs for each new domain
                extractor.visited_urls.clear()
                
            except Exception as e:
                logger.error(f"Error processing {source['link']}: {e}")
                # Add original source if extraction fails
                all_extracted_sources.append(source)
        
        # Save extracted links
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_extracted_sources, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved {len(all_extracted_sources)} extracted sources to {output_file}")
        
        # Create merged file (original + extracted, removing duplicates)
        merged_sources = []
        seen_links = set()
        
        # Add all extracted sources (which includes originals)
        for source in all_extracted_sources:
            if source['link'] not in seen_links:
                merged_sources.append(source)
                seen_links.add(source['link'])
        
        # Save merged file
        with open(merged_file, 'w', encoding='utf-8') as f:
            json.dump(merged_sources, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Created merged file with {len(merged_sources)} unique sources: {merged_file}")
        
        return merged_file
        
    except Exception as e:
        logger.error(f"Error in extract_all_links: {e}")
        raise

if __name__ == "__main__":
    # Test the link extractor
    merged_file = extract_all_links()
    print(f"Link extraction completed. Merged file: {merged_file}")
