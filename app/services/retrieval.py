"""
Vector retrieval service using FAISS.
"""
import faiss
import numpy as np
import logging
from typing import List, Dict, Any, Tuple
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VectorStore:
    """Vector store for document retrieval using FAISS."""
    
    def __init__(self):
        self.index = None
        self.documents = []
        self.dimension = None
    
    def create_index(self, embeddings: List[List[float]], documents: List[Dict[str, Any]]) -> None:
        """
        Create FAISS index from embeddings and store documents.
        
        Args:
            embeddings: List of embedding vectors
            documents: List of corresponding documents
        """
        if not embeddings or not documents:
            raise ValueError("Embeddings and documents cannot be empty")
        
        if len(embeddings) != len(documents):
            raise ValueError("Number of embeddings must match number of documents")
        
        # Convert embeddings to numpy array
        embedding_matrix = np.array(embeddings, dtype=np.float32)
        self.dimension = embedding_matrix.shape[1]
        
        # Create FAISS index
        self.index = faiss.IndexFlatL2(self.dimension)
        self.index.add(embedding_matrix)
        
        # Store documents
        self.documents = documents
        
        logger.info(f"Created FAISS index with {len(documents)} documents, dimension: {self.dimension}")
    
    def search(self, query_embedding: List[float], top_k: int = None) -> List[Tuple[Dict[str, Any], float]]:
        """
        Search for similar documents using query embedding.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of top results to return
            
        Returns:
            List of tuples containing (document, distance)
        """
        if self.index is None:
            raise ValueError("Index not created. Call create_index first.")
        
        if top_k is None:
            top_k = settings.TOP_K_RESULTS
        
        # Ensure query embedding is the right dimension
        query_vector = np.array([query_embedding], dtype=np.float32)
        
        if query_vector.shape[1] != self.dimension:
            raise ValueError(f"Query embedding dimension {query_vector.shape[1]} doesn't match index dimension {self.dimension}")
        
        # Search the index
        distances, indices = self.index.search(query_vector, min(top_k, len(self.documents)))
        
        # Return documents with their distances
        results = []
        for distance, idx in zip(distances[0], indices[0]):
            if idx < len(self.documents):  # Valid index
                results.append((self.documents[idx], float(distance)))
        
        logger.info(f"Retrieved {len(results)} documents for query")
        return results
    
    def get_context(self, query_embedding: List[float], top_k: int = None) -> str:
        """
        Get context string from retrieved documents.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of top results to use for context
            
        Returns:
            Combined context string from retrieved documents
        """
        results = self.search(query_embedding, top_k)
        
        context_parts = []
        total_length = 0
        
        for doc, distance in results:
            content = doc['content']
            title = doc.get('title', 'Unknown')
            
            # Add document with title
            doc_text = f"Document: {title}\nContent: {content}\n\n"
            
            # Check if adding this document would exceed max context length
            if total_length + len(doc_text) > settings.MAX_CONTEXT_LENGTH:
                # Truncate the content to fit
                remaining_space = settings.MAX_CONTEXT_LENGTH - total_length
                if remaining_space > 100:  # Only add if there's meaningful space
                    truncated_content = content[:remaining_space - 50]
                    doc_text = f"Document: {title}\nContent: {truncated_content}...\n\n"
                    context_parts.append(doc_text)
                break
            
            context_parts.append(doc_text)
            total_length += len(doc_text)
        
        context = "".join(context_parts)
        logger.info(f"Generated context with {len(context)} characters from {len(context_parts)} documents")
        return context

# Global vector store instance
vector_store = VectorStore()
