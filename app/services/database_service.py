"""
Database service for chat operations.
"""
import json
import logging
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.models import Conversation, Message
from app.database import get_db_session

logger = logging.getLogger(__name__)


class DatabaseService:
    """Service class for database operations related to chat functionality."""
    
    @staticmethod
    def create_conversation() -> Optional[str]:
        """
        Create a new conversation and return its ID.
        
        Returns:
            str: Conversation ID if successful, None if failed
        """
        try:
            with get_db_session() as db:
                conversation = Conversation()
                db.add(conversation)
                db.flush()  # Flush to get the ID
                conversation_id = conversation.id
                logger.info(f"Created new conversation: {conversation_id}")
                return conversation_id
        except SQLAlchemyError as e:
            logger.error(f"Error creating conversation: {e}")
            return None
    
    @staticmethod
    def save_user_message(conversation_id: str, content: str) -> Optional[str]:
        """
        Save a user message to the database.
        
        Args:
            conversation_id: ID of the conversation
            content: Message content
            
        Returns:
            str: Message ID if successful, None if failed
        """
        try:
            with get_db_session() as db:
                message = Message(
                    conversation_id=conversation_id,
                    content=content,
                    message_type="user"
                )
                db.add(message)
                db.flush()
                message_id = message.id
                logger.info(f"Saved user message: {message_id}")
                return message_id
        except SQLAlchemyError as e:
            logger.error(f"Error saving user message: {e}")
            return None
    
    @staticmethod
    def save_bot_response(conversation_id: str, content: str, sources: Optional[List[Dict[str, Any]]] = None) -> Optional[str]:
        """
        Save a bot response to the database.
        
        Args:
            conversation_id: ID of the conversation
            content: Response content
            sources: List of sources used for the response
            
        Returns:
            str: Message ID if successful, None if failed
        """
        try:
            with get_db_session() as db:
                # Convert sources to JSON string if provided
                sources_json = json.dumps(sources) if sources else None
                
                message = Message(
                    conversation_id=conversation_id,
                    content=content,
                    message_type="bot",
                    sources=sources_json
                )
                db.add(message)
                db.flush()
                message_id = message.id
                logger.info(f"Saved bot response: {message_id}")
                return message_id
        except SQLAlchemyError as e:
            logger.error(f"Error saving bot response: {e}")
            return None
    
    @staticmethod
    def get_conversation_history(conversation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get conversation history.
        
        Args:
            conversation_id: ID of the conversation
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of message dictionaries
        """
        try:
            with get_db_session() as db:
                messages = db.query(Message).filter(
                    Message.conversation_id == conversation_id
                ).order_by(Message.created_at).limit(limit).all()
                
                history = []
                for message in messages:
                    message_dict = {
                        "id": message.id,
                        "content": message.content,
                        "type": message.message_type,
                        "created_at": message.created_at.isoformat(),
                        "sources": json.loads(message.sources) if message.sources else None
                    }
                    history.append(message_dict)
                
                logger.info(f"Retrieved {len(history)} messages for conversation {conversation_id}")
                return history
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving conversation history: {e}")
            return []
    
    @staticmethod
    def conversation_exists(conversation_id: str) -> bool:
        """
        Check if a conversation exists.
        
        Args:
            conversation_id: ID of the conversation
            
        Returns:
            bool: True if conversation exists, False otherwise
        """
        try:
            with get_db_session() as db:
                conversation = db.query(Conversation).filter(
                    Conversation.id == conversation_id
                ).first()
                return conversation is not None
        except SQLAlchemyError as e:
            logger.error(f"Error checking conversation existence: {e}")
            return False
