"""
Database service for document embeddings.
"""
import json
import hashlib
import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.models import Document, DocumentEmbedding
from app.database import get_db_session
from app.config import settings

logger = logging.getLogger(__name__)


class EmbeddingService:
    """Service class for managing document embeddings in the database."""
    
    @staticmethod
    def _calculate_content_hash(content: str) -> str:
        """Calculate hash of content to detect changes."""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    @staticmethod
    def save_document_with_embedding(
        title: str, 
        url: str, 
        content: str, 
        embedding_vector: List[float],
        embedding_model: str = None
    ) -> Optional[str]:
        """
        Save document and its embedding to database.
        
        Args:
            title: Document title
            url: Document URL
            content: Document content
            embedding_vector: Embedding vector
            embedding_model: Model used for embedding
            
        Returns:
            Document ID if successful, None if failed
        """
        if embedding_model is None:
            embedding_model = settings.EMBEDDING_MODEL
            
        try:
            with get_db_session() as db:
                content_hash = EmbeddingService._calculate_content_hash(content)
                
                # Check if document already exists
                existing_doc = db.query(Document).filter(Document.url == url).first()
                
                if existing_doc:
                    # Check if content has changed
                    if existing_doc.content_hash == content_hash:
                        logger.info(f"Document unchanged, skipping: {url}")
                        return existing_doc.id
                    else:
                        # Update existing document
                        existing_doc.title = title
                        existing_doc.content = content
                        existing_doc.content_hash = content_hash
                        
                        # Update embedding
                        if existing_doc.embedding:
                            existing_doc.embedding.embedding_vector = embedding_vector
                            existing_doc.embedding.embedding_model = embedding_model
                        else:
                            # Create new embedding
                            new_embedding = DocumentEmbedding(
                                document_id=existing_doc.id,
                                embedding_vector=embedding_vector,
                                embedding_model=embedding_model
                            )
                            db.add(new_embedding)
                        
                        logger.info(f"Updated document: {url}")
                        return existing_doc.id
                else:
                    # Create new document
                    new_document = Document(
                        title=title,
                        url=url,
                        content=content,
                        content_hash=content_hash
                    )
                    db.add(new_document)
                    db.flush()  # Get the document ID
                    
                    # Create embedding
                    new_embedding = DocumentEmbedding(
                        document_id=new_document.id,
                        embedding_vector=embedding_vector,
                        embedding_model=embedding_model
                    )
                    db.add(new_embedding)
                    
                    logger.info(f"Saved new document: {url}")
                    return new_document.id
                    
        except SQLAlchemyError as e:
            logger.error(f"Error saving document with embedding: {e}")
            return None
    
    @staticmethod
    def load_all_embeddings() -> Tuple[List[List[float]], List[Dict[str, Any]]]:
        """
        Load all embeddings and documents from database.
        
        Returns:
            Tuple of (embeddings_list, documents_list)
        """
        try:
            with get_db_session() as db:
                # Query documents with their embeddings
                results = db.query(Document, DocumentEmbedding).join(
                    DocumentEmbedding, Document.id == DocumentEmbedding.document_id
                ).all()
                
                embeddings = []
                documents = []
                
                for doc, embedding in results:
                    embeddings.append(embedding.embedding_vector)
                    documents.append({
                        'id': doc.id,
                        'title': doc.title,
                        'url': doc.url,
                        'content': doc.content,
                        'created_at': doc.created_at.isoformat() if doc.created_at else None
                    })
                
                logger.info(f"Loaded {len(embeddings)} embeddings from database")
                return embeddings, documents
                
        except SQLAlchemyError as e:
            logger.error(f"Error loading embeddings: {e}")
            return [], []
    
    @staticmethod
    def get_documents_count() -> int:
        """Get total number of documents in database."""
        try:
            with get_db_session() as db:
                count = db.query(Document).count()
                return count
        except SQLAlchemyError as e:
            logger.error(f"Error getting documents count: {e}")
            return 0
    
    @staticmethod
    def clear_all_documents() -> bool:
        """Clear all documents and embeddings from database."""
        try:
            with get_db_session() as db:
                # Delete all embeddings first (due to foreign key)
                db.query(DocumentEmbedding).delete()
                # Delete all documents
                db.query(Document).delete()
                
                logger.info("Cleared all documents and embeddings from database")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Error clearing documents: {e}")
            return False
    
    @staticmethod
    def get_embedding_stats() -> Dict[str, Any]:
        """Get statistics about stored embeddings."""
        try:
            with get_db_session() as db:
                total_docs = db.query(Document).count()
                total_embeddings = db.query(DocumentEmbedding).count()
                
                # Get model distribution
                from sqlalchemy import func
                model_stats = db.query(
                    DocumentEmbedding.embedding_model,
                    func.count(DocumentEmbedding.id)
                ).group_by(DocumentEmbedding.embedding_model).all()
                
                return {
                    'total_documents': total_docs,
                    'total_embeddings': total_embeddings,
                    'models_used': dict(model_stats),
                    'has_embeddings': total_embeddings > 0
                }
                
        except SQLAlchemyError as e:
            logger.error(f"Error getting embedding stats: {e}")
            return {
                'total_documents': 0,
                'total_embeddings': 0,
                'models_used': {},
                'has_embeddings': False
            }
    
    @staticmethod
    def batch_save_documents_with_embeddings(
        documents: List[Dict[str, Any]], 
        embeddings: List[List[float]],
        embedding_model: str = None
    ) -> int:
        """
        Batch save multiple documents with their embeddings.
        
        Args:
            documents: List of document dictionaries
            embeddings: List of embedding vectors
            embedding_model: Model used for embeddings
            
        Returns:
            Number of documents saved
        """
        if len(documents) != len(embeddings):
            logger.error("Documents and embeddings lists must have same length")
            return 0
        
        if embedding_model is None:
            embedding_model = settings.EMBEDDING_MODEL
        
        saved_count = 0
        
        for doc, embedding in zip(documents, embeddings):
            doc_id = EmbeddingService.save_document_with_embedding(
                title=doc.get('title', 'Unknown'),
                url=doc.get('url', ''),
                content=doc.get('content', ''),
                embedding_vector=embedding,
                embedding_model=embedding_model
            )
            
            if doc_id:
                saved_count += 1
        
        logger.info(f"Batch saved {saved_count}/{len(documents)} documents with embeddings")
        return saved_count
