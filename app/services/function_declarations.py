"""
Function declarations for Google GenAI tool calling.
"""

# Support contact function declaration
get_support_contact_decl = {
    "name": "get_support_contact",
    "description": "Get appropriate support contact information based on the type of customer issue or problem.",
    "parameters": {
        "type": "object",
        "properties": {
            "issue": {
                "type": "string",
                "description": "Brief description of the customer's issue (e.g., billing problem, API error, login issue, captcha accuracy)"
            }
        },
        "required": ["issue"]
    }
}

# Service status function declaration
get_service_status_decl = {
    "name": "get_service_status",
    "description": "Get current operational status of NoCaptcha services and systems.",
    "parameters": {
        "type": "object", 
        "properties": {},
        "required": []
    }
}

# Refund policy function declaration
get_refund_policy_decl = {
    "name": "get_refund_policy",
    "description": "Get information about refund policy and process.",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": []
    }
}

# Payment methods function declaration
get_payment_methods_decl = {
    "name": "get_payment_methods",
    "description": "Get information about available payment methods and billing options.",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": []
    }
}

# Captcha help function declaration
get_captcha_help_decl = {
    "name": "get_captcha_help",
    "description": "Get help with captcha-related issues and troubleshooting.",
    "parameters": {
        "type": "object",
        "properties": {
            "issue_type": {
                "type": "string",
                "description": "Type of captcha issue (accuracy, speed, general)",
                "enum": ["accuracy", "speed", "general"]
            }
        },
        "required": []
    }
}

# Issue escalation function declaration
escalate_issue_decl = {
    "name": "escalate_issue",
    "description": "Escalate a customer issue to appropriate support team based on urgency level.",
    "parameters": {
        "type": "object",
        "properties": {
            "issue": {
                "type": "string",
                "description": "Description of the issue that needs escalation"
            },
            "urgency": {
                "type": "string",
                "description": "Urgency level of the issue",
                "enum": ["low", "normal", "high", "critical"]
            }
        },
        "required": ["issue"]
    }
}

# List of all available function declarations
ALL_FUNCTION_DECLARATIONS = [
    get_support_contact_decl,
    get_service_status_decl,
    get_refund_policy_decl,
    get_payment_methods_decl,
    get_captcha_help_decl,
    escalate_issue_decl
]
