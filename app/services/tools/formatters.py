"""
Result formatting for tool calling responses.
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class ToolResultFormatter:
    """Formats tool execution results into user-friendly text."""
    
    def format_result(self, result: Dict[str, Any], function_name: str) -> str:
        """
        Format function result into user-friendly text.
        
        Args:
            result: Function execution result
            function_name: Name of the executed function
            
        Returns:
            Formatted text response
        """
        if "error" in result:
            return f"I encountered an error: {result['error']}"
        
        try:
            if function_name == "get_support_contact":
                return self._format_support_contact(result)
            elif function_name == "get_pricing_info":
                return self._format_pricing_info(result)
            elif function_name == "get_service_status":
                return self._format_service_status(result)
            elif function_name == "get_refund_policy":
                return self._format_refund_policy(result)
            elif function_name == "get_payment_methods":
                return self._format_payment_methods(result)
            elif function_name == "get_captcha_help":
                return self._format_captcha_help(result)
            elif function_name == "escalate_issue":
                return self._format_escalation(result)
            else:
                # Generic formatting
                return self._format_generic(result)
                
        except Exception as e:
            logger.error(f"Error formatting result for {function_name}: {e}")
            return f"I found some information but had trouble formatting it. <NAME_EMAIL> for assistance."
    
    def _format_support_contact(self, result: Dict[str, Any]) -> str:
        """Format support contact information."""
        department = result.get("department", "Support")
        email = result.get("email", "")
        description = result.get("description", "")
        hours = result.get("hours", "")
        response_time = result.get("response_time", "")
        
        text = f"For your issue, please contact our **{department}**.\n\n"
        
        if email:
            text += f"📧 **Email:** {email}\n"
        
        if description:
            text += f"📝 **Description:** {description}\n"
        
        if hours:
            text += f"🕒 **Hours:** {hours}\n"
        
        if response_time:
            text += f"⏱️ **Response Time:** {response_time}\n"
        
        # Add additional contact methods if available
        if "documentation" in result:
            text += f"📚 **Documentation:** {result['documentation']}\n"
        
        if "github" in result:
            text += f"💻 **GitHub:** {result['github']}\n"
        
        if "website" in result:
            text += f"🌐 **Website:** {result['website']}\n"
        
        return text
    
    def _format_pricing_info(self, result: Dict[str, Any]) -> str:
        """Format pricing information."""
        plan = result.get("plan", "Pricing")
        description = result.get("description", "")
        
        text = f"## {plan}\n\n"
        
        if description:
            text += f"{description}\n\n"
        
        if "starting_price" in result:
            text += f"💰 **Starting Price:** {result['starting_price']}\n"
        
        if "pricing" in result:
            text += f"💰 **Pricing:** {result['pricing']}\n"
        
        if "features" in result:
            text += "✨ **Features:**\n"
            for feature in result["features"]:
                text += f"  • {feature}\n"
            text += "\n"
        
        if "pricing_url" in result:
            text += f"🔗 **Full Pricing:** {result['pricing_url']}\n"
        
        if "contact" in result:
            text += f"📧 **Contact:** {result['contact']}\n"
        
        return text
    
    def _format_service_status(self, result: Dict[str, Any]) -> str:
        """Format service status information."""
        status = result.get("status", "Unknown")
        description = result.get("description", "")
        
        status_emoji = "🟢" if status.lower() == "operational" else "🔴"
        
        text = f"{status_emoji} **Service Status: {status}**\n\n"
        
        if description:
            text += f"{description}\n\n"
        
        if "uptime" in result:
            text += f"📊 **Uptime:** {result['uptime']}\n"
        
        if "services" in result:
            text += "**Individual Services:**\n"
            for service, service_status in result["services"].items():
                service_emoji = "🟢" if service_status.lower() == "operational" else "🔴"
                text += f"  {service_emoji} {service}: {service_status}\n"
            text += "\n"
        
        if "status_page" in result:
            text += f"🔗 **Status Page:** {result['status_page']}\n"
        
        return text
    
    def _format_escalation(self, result: Dict[str, Any]) -> str:
        """Format escalation information."""
        level = result.get("escalation_level", "Support")
        contact = result.get("contact", "")
        description = result.get("description", "")
        
        text = f"🚨 **Escalation Level:** {level}\n\n"
        
        if description:
            text += f"{description}\n\n"
        
        if contact:
            text += f"📧 **Contact:** {contact}\n"
        
        if "phone" in result:
            text += f"📞 **Phone:** {result['phone']}\n"
        
        if "response_time" in result:
            text += f"⏱️ **Response Time:** {result['response_time']}\n"
        
        if "availability" in result:
            text += f"🕒 **Availability:** {result['availability']}\n"

        return text

    def _format_refund_policy(self, result: Dict[str, Any]) -> str:
        """Format refund policy information."""
        policy = result.get("policy", "Refund Policy")
        description = result.get("description", "")

        text = f"## {policy}\n\n"

        if description:
            text += f"{description}\n\n"

        if "conditions" in result:
            text += "📋 **Conditions:**\n"
            for condition in result["conditions"]:
                text += f"  • {condition}\n"
            text += "\n"

        if "requirements" in result:
            text += "📝 **Requirements:**\n"
            for req in result["requirements"]:
                text += f"  • {req}\n"
            text += "\n"

        if "process_time" in result:
            text += f"⏱️ **Processing Time:** {result['process_time']}\n"

        if "contact" in result:
            text += f"📧 **Contact:** {result['contact']}\n"

        return text

    def _format_payment_methods(self, result: Dict[str, Any]) -> str:
        """Format payment methods information."""
        text = "## Payment Methods\n\n"

        if "accepted_methods" in result:
            text += "💳 **Accepted Payment Methods:**\n"
            for method in result["accepted_methods"]:
                text += f"  • {method}\n"
            text += "\n"

        if "billing_cycle" in result:
            text += f"🔄 **Billing Cycle:** {result['billing_cycle']}\n"

        if "currency" in result:
            text += f"💰 **Currency:** {result['currency']}\n"

        if "auto_renewal" in result:
            text += f"🔁 **Auto-Renewal:** {result['auto_renewal']}\n"

        if "payment_security" in result:
            text += f"🔒 **Security:** {result['payment_security']}\n"

        if "enterprise_options" in result:
            text += "\n**Enterprise Options:**\n"
            for key, value in result["enterprise_options"].items():
                text += f"  • {key.replace('_', ' ').title()}: {value}\n"

        if "contact" in result:
            text += f"\n📧 **Contact:** {result['contact']}\n"

        return text

    def _format_captcha_help(self, result: Dict[str, Any]) -> str:
        """Format captcha help information."""
        issue = result.get("issue", "Captcha Help")
        description = result.get("description", "")

        text = f"## {issue}\n\n"

        if description:
            text += f"{description}\n\n"

        if "common_causes" in result:
            text += "🔍 **Common Causes:**\n"
            for cause in result["common_causes"]:
                text += f"  • {cause}\n"
            text += "\n"

        if "solutions" in result:
            text += "✅ **Solutions:**\n"
            for solution in result["solutions"]:
                text += f"  • {solution}\n"
            text += "\n"

        if "typical_times" in result:
            text += "⏱️ **Typical Solving Times:**\n"
            for captcha_type, time in result["typical_times"].items():
                text += f"  • {captcha_type}: {time}\n"
            text += "\n"

        if "optimization_tips" in result:
            text += "🚀 **Optimization Tips:**\n"
            for tip in result["optimization_tips"]:
                text += f"  • {tip}\n"
            text += "\n"

        if "supported_types" in result:
            text += "🎯 **Supported Captcha Types:**\n"
            for captcha_type in result["supported_types"]:
                text += f"  • {captcha_type}\n"
            text += "\n"

        if "documentation" in result:
            text += f"📚 **Documentation:** {result['documentation']}\n"

        if "api_guide" in result:
            text += f"🔗 **API Guide:** {result['api_guide']}\n"

        if "contact" in result:
            text += f"📧 **Contact:** {result['contact']}\n"

        return text

    def _format_generic(self, result: Dict[str, Any]) -> str:
        """Generic formatting for unknown function results."""
        text = "Here's the information I found:\n\n"

        for key, value in result.items():
            if isinstance(value, (str, int, float)):
                text += f"**{key.replace('_', ' ').title()}:** {value}\n"
            elif isinstance(value, list):
                text += f"**{key.replace('_', ' ').title()}:**\n"
                for item in value:
                    text += f"  • {item}\n"

        return text
