"""
Tool execution service for function calling.
"""
import logging
from typing import Dict, Any, Optional
import json

from app.services.support_utils import (
    get_support_contact,
    get_pricing_info,
    get_service_status,
    get_refund_policy,
    get_payment_methods,
    get_captcha_help,
    escalate_issue
)
from .formatters import ToolResultFormatter

logger = logging.getLogger(__name__)


class ToolCallingService:
    """Service for executing function calls from LLM."""
    
    def __init__(self):
        """Initialize the tool calling service."""
        self.available_functions = {
            "get_support_contact": get_support_contact,
            "get_pricing_info": get_pricing_info,
            "get_service_status": get_service_status,
            "get_refund_policy": get_refund_policy,
            "get_payment_methods": get_payment_methods,
            "get_captcha_help": get_captcha_help,
            "escalate_issue": escalate_issue
        }
        self.formatter = ToolResultFormatter()
    
    def execute_function(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a function call with given arguments.
        
        Args:
            function_name: Name of the function to call
            arguments: Function arguments
            
        Returns:
            Function execution result
        """
        try:
            if function_name not in self.available_functions:
                logger.error(f"Unknown function: {function_name}")
                return {
                    "error": f"Function '{function_name}' is not available",
                    "available_functions": list(self.available_functions.keys())
                }
            
            # Get the function
            func = self.available_functions[function_name]
            
            # Execute with arguments
            logger.info(f"Executing function: {function_name} with args: {arguments}")
            result = func(**arguments)
            
            logger.info(f"Function {function_name} executed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error executing function {function_name}: {e}")
            return {
                "error": f"Error executing {function_name}: {str(e)}",
                "function": function_name,
                "arguments": arguments
            }
    
    def format_function_result(self, result: Dict[str, Any], function_name: str) -> str:
        """
        Format function result into user-friendly text.
        
        Args:
            result: Function execution result
            function_name: Name of the executed function
            
        Returns:
            Formatted text response
        """
        return self.formatter.format_result(result, function_name)
    
    def get_available_functions(self) -> list:
        """Get list of available function names."""
        return list(self.available_functions.keys())
    
    def get_function_info(self, function_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific function.
        
        Args:
            function_name: Name of the function
            
        Returns:
            Function information or None if not found
        """
        if function_name not in self.available_functions:
            return None
        
        func = self.available_functions[function_name]
        return {
            "name": function_name,
            "doc": func.__doc__,
            "module": func.__module__
        }


# Global instance
tool_calling_service = ToolCallingService()
