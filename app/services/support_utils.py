"""
Support utility functions for customer service chatbot.
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def get_support_contact(issue: str) -> Dict[str, Any]:
    """
    Return support contact information based on the issue type.
    
    Args:
        issue: Description of the user's issue
        
    Returns:
        Dictionary with support contact information
    """
    issue_lower = issue.lower()
    
    # NoCaptcha specific support routing
    if any(word in issue_lower for word in ["billing", "payment", "subscription", "plan", "pricing"]):
        return {
            "department": "Billing Support",
            "email": "<EMAIL>",
            "description": "For billing, payment, and subscription issues",
            "hours": "Mon–Fri 9am–6pm UTC",
            "response_time": "Within 24 hours"
        }
    
    elif any(word in issue_lower for word in ["api", "integration", "code", "error", "bug", "technical"]):
        return {
            "department": "Technical Support", 
            "email": "<EMAIL>",
            "description": "For API integration and technical issues",
            "documentation": "https://docs.nocaptchaai.com/",
            "github": "https://github.com/noCaptchaAi/NoCaptcha-Ai-Browser-Extension",
            "hours": "24/7 support available",
            "response_time": "Within 4 hours"
        }
    
    elif any(word in issue_lower for word in ["account", "login", "password", "access", "signup"]):
        return {
            "department": "Account Support",
            "email": "<EMAIL>", 
            "description": "For account access and login issues",
            "hours": "Mon–Fri 8am–8pm UTC",
            "response_time": "Within 12 hours"
        }
    
    elif any(word in issue_lower for word in ["captcha", "solve", "accuracy", "speed", "performance"]):
        return {
            "department": "Service Quality Team",
            "email": "<EMAIL>",
            "description": "For captcha solving accuracy and performance issues", 
            "hours": "24/7 monitoring",
            "response_time": "Within 2 hours"
        }
    
    else:
        return {
            "department": "General Support",
            "email": "<EMAIL>",
            "description": "For general inquiries and other issues",
            "website": "https://nocaptchaai.com/",
            "hours": "Mon–Fri 9am–6pm UTC",
            "response_time": "Within 24 hours"
        }


def get_pricing_info(service_type: str = "general") -> Dict[str, Any]:
    """
    Return pricing information for NoCaptcha services.
    
    Args:
        service_type: Type of service (general, api, enterprise)
        
    Returns:
        Dictionary with pricing information
    """
    service_lower = service_type.lower()
    
    if "enterprise" in service_lower:
        return {
            "plan": "Enterprise",
            "description": "Custom solutions for high-volume users",
            "contact": "<EMAIL>",
            "features": [
                "Dedicated support",
                "Custom pricing",
                "SLA guarantees",
                "Priority processing"
            ],
            "pricing": "Contact for custom quote"
        }
    
    elif "api" in service_lower:
        return {
            "plan": "API Access",
            "description": "Pay-per-use API for developers",
            "pricing_url": "https://nocaptchaai.com/pricing",
            "features": [
                "RESTful API",
                "Multiple captcha types",
                "Fast response times",
                "Detailed documentation"
            ],
            "starting_price": "Starting from $0.001 per solve"
        }
    
    else:
        return {
            "plan": "General Pricing",
            "description": "Flexible pricing for all users",
            "pricing_url": "https://nocaptchaai.com/pricing",
            "contact": "<EMAIL>",
            "features": [
                "Multiple pricing tiers",
                "Volume discounts available",
                "Free trial available",
                "No setup fees"
            ]
        }


def get_service_status() -> Dict[str, Any]:
    """
    Return current service status information.
    
    Returns:
        Dictionary with service status
    """
    return {
        "status": "Operational",
        "description": "All NoCaptcha services are running normally",
        "uptime": "99.9%",
        "last_updated": "Real-time monitoring",
        "status_page": "https://status.nocaptchaai.com/",
        "services": {
            "API": "Operational",
            "Web Interface": "Operational", 
            "Browser Extension": "Operational",
            "Support System": "Operational"
        },
        "contact": "<EMAIL>"
    }


def get_refund_policy() -> Dict[str, Any]:
    """
    Get refund policy information.

    Returns:
        Dictionary with refund policy details
    """
    return {
        "policy": "30-Day Money Back Guarantee",
        "description": "We offer a full refund within 30 days of purchase if you're not satisfied",
        "conditions": [
            "Request must be made within 30 days of purchase",
            "Account must not have excessive usage",
            "Refund processed to original payment method"
        ],
        "contact": "<EMAIL>",
        "process_time": "3-5 business days",
        "requirements": [
            "Original purchase receipt or transaction ID",
            "Reason for refund request",
            "Account email address"
        ]
    }


def get_payment_methods() -> Dict[str, Any]:
    """
    Get available payment methods.

    Returns:
        Dictionary with payment method information
    """
    return {
        "accepted_methods": [
            "Credit Cards (Visa, MasterCard, American Express)",
            "PayPal",
            "Bank Transfer (for enterprise accounts)",
            "Cryptocurrency (Bitcoin, Ethereum)"
        ],
        "billing_cycle": "Monthly or Annual",
        "currency": "USD",
        "auto_renewal": "Enabled by default (can be disabled)",
        "payment_security": "PCI DSS compliant",
        "contact": "<EMAIL>",
        "enterprise_options": {
            "custom_billing": "Available for enterprise plans",
            "invoicing": "Net 30 terms available",
            "purchase_orders": "Accepted for qualified accounts"
        }
    }


def get_captcha_help(issue_type: str = "general") -> Dict[str, Any]:
    """
    Get help with captcha-related issues.

    Args:
        issue_type: Type of captcha issue

    Returns:
        Dictionary with captcha help information
    """
    issue_lower = issue_type.lower()

    if "accuracy" in issue_lower or "wrong" in issue_lower:
        return {
            "issue": "Captcha Accuracy Problems",
            "description": "Help with incorrect captcha solutions",
            "common_causes": [
                "Image quality issues",
                "Captcha type mismatch",
                "Outdated captcha challenges"
            ],
            "solutions": [
                "Verify captcha type in API request",
                "Check image quality and format",
                "Update to latest API version"
            ],
            "contact": "<EMAIL>",
            "documentation": "https://docs.nocaptchaai.com/troubleshooting"
        }

    elif "slow" in issue_lower or "speed" in issue_lower:
        return {
            "issue": "Captcha Solving Speed",
            "description": "Help with slow captcha solving times",
            "typical_times": {
                "reCAPTCHA v2": "10-30 seconds",
                "reCAPTCHA v3": "5-15 seconds",
                "hCaptcha": "15-45 seconds",
                "FunCaptcha": "20-60 seconds"
            },
            "optimization_tips": [
                "Use correct captcha type parameter",
                "Ensure good image quality",
                "Check server load status"
            ],
            "contact": "<EMAIL>"
        }

    else:
        return {
            "issue": "General Captcha Help",
            "description": "General assistance with captcha solving",
            "supported_types": [
                "reCAPTCHA v2 & v3",
                "hCaptcha",
                "FunCaptcha",
                "GeeTest",
                "Cloudflare Turnstile"
            ],
            "contact": "<EMAIL>",
            "documentation": "https://docs.nocaptchaai.com/",
            "api_guide": "https://docs.nocaptchaai.com/api"
        }


def escalate_issue(issue: str, urgency: str = "normal") -> Dict[str, Any]:
    """
    Escalate an issue to appropriate team based on urgency.

    Args:
        issue: Description of the issue
        urgency: Urgency level (low, normal, high, critical)

    Returns:
        Dictionary with escalation information
    """
    urgency_lower = urgency.lower()

    if urgency_lower in ["critical", "urgent", "high"]:
        return {
            "escalation_level": "High Priority",
            "contact": "<EMAIL>",
            "description": "Critical issues receive immediate attention",
            "response_time": "Within 1 hour",
            "availability": "24/7 emergency support",
            "phone_support": "Available for enterprise customers"
        }

    elif urgency_lower == "low":
        return {
            "escalation_level": "Standard Queue",
            "contact": "<EMAIL>",
            "description": "Standard support queue for non-urgent issues",
            "response_time": "Within 48 hours",
            "availability": "Business hours"
        }

    else:
        return {
            "escalation_level": "Normal Priority",
            "contact": "<EMAIL>",
            "description": "Normal priority support queue",
            "response_time": "Within 24 hours",
            "availability": "Extended business hours"
        }
