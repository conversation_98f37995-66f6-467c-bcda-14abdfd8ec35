"""
Billing and payment related support functions.
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def get_pricing_info(service_type: str = "general") -> Dict[str, Any]:
    """
    Return pricing information for NoCaptcha services.
    
    Args:
        service_type: Type of service (general, api, enterprise)
        
    Returns:
        Dictionary with pricing information
    """
    service_lower = service_type.lower()
    
    if "enterprise" in service_lower:
        return {
            "plan": "Enterprise",
            "description": "Custom solutions for high-volume users",
            "contact": "<EMAIL>",
            "features": [
                "Dedicated support",
                "Custom pricing",
                "SLA guarantees",
                "Priority processing"
            ],
            "pricing": "Contact for custom quote"
        }
    
    elif "api" in service_lower:
        return {
            "plan": "API Access",
            "description": "Pay-per-use API for developers",
            "pricing_url": "https://nocaptchaai.com/pricing",
            "features": [
                "RESTful API",
                "Multiple captcha types",
                "Fast response times",
                "Detailed documentation"
            ],
            "starting_price": "Starting from $0.001 per solve"
        }
    
    else:
        return {
            "plan": "General Pricing",
            "description": "Flexible pricing for all users",
            "pricing_url": "https://nocaptchaai.com/pricing",
            "contact": "<EMAIL>",
            "features": [
                "Multiple pricing tiers",
                "Volume discounts available",
                "Free trial available",
                "No setup fees"
            ]
        }


def get_refund_policy() -> Dict[str, Any]:
    """
    Get refund policy information.

    Returns:
        Dictionary with refund policy details
    """
    return {
        "policy": "30-Day Money Back Guarantee",
        "description": "We offer a full refund within 30 days of purchase if you're not satisfied",
        "conditions": [
            "Request must be made within 30 days of purchase",
            "Account must not have excessive usage",
            "Refund processed to original payment method"
        ],
        "contact": "<EMAIL>",
        "process_time": "3-5 business days",
        "requirements": [
            "Original purchase receipt or transaction ID",
            "Reason for refund request",
            "Account email address"
        ]
    }


def get_payment_methods() -> Dict[str, Any]:
    """
    Get available payment methods.

    Returns:
        Dictionary with payment method information
    """
    return {
        "accepted_methods": [
            "Credit Cards (Visa, MasterCard, American Express)",
            "PayPal",
            "Bank Transfer (for enterprise accounts)",
            "Cryptocurrency (Bitcoin, Ethereum)"
        ],
        "billing_cycle": "Monthly or Annual",
        "currency": "USD",
        "auto_renewal": "Enabled by default (can be disabled)",
        "payment_security": "PCI DSS compliant",
        "contact": "<EMAIL>",
        "enterprise_options": {
            "custom_billing": "Available for enterprise plans",
            "invoicing": "Net 30 terms available",
            "purchase_orders": "Accepted for qualified accounts"
        }
    }
