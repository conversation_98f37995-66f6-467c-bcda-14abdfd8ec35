"""
Technical support and service status functions.
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def get_service_status() -> Dict[str, Any]:
    """
    Return current service status information.
    
    Returns:
        Dictionary with service status
    """
    return {
        "status": "Operational",
        "description": "All NoCaptcha services are running normally",
        "uptime": "99.9%",
        "last_updated": "Real-time monitoring",
        "status_page": "https://status.nocaptchaai.com/",
        "services": {
            "API": "Operational",
            "Web Interface": "Operational", 
            "Browser Extension": "Operational",
            "Support System": "Operational"
        },
        "contact": "<EMAIL>"
    }


def get_captcha_help(issue_type: str = "general") -> Dict[str, Any]:
    """
    Get help with captcha-related issues.

    Args:
        issue_type: Type of captcha issue

    Returns:
        Dictionary with captcha help information
    """
    issue_lower = issue_type.lower()

    if "accuracy" in issue_lower or "wrong" in issue_lower:
        return {
            "issue": "Captcha Accuracy Problems",
            "description": "Help with incorrect captcha solutions",
            "common_causes": [
                "Image quality issues",
                "Captcha type mismatch",
                "Outdated captcha challenges"
            ],
            "solutions": [
                "Verify captcha type in API request",
                "Check image quality and format",
                "Update to latest API version"
            ],
            "contact": "<EMAIL>",
            "documentation": "https://docs.nocaptchaai.com/troubleshooting"
        }

    elif "slow" in issue_lower or "speed" in issue_lower:
        return {
            "issue": "Captcha Solving Speed",
            "description": "Help with slow captcha solving times",
            "typical_times": {
                "reCAPTCHA v2": "10-30 seconds",
                "reCAPTCHA v3": "5-15 seconds",
                "hCaptcha": "15-45 seconds",
                "FunCaptcha": "20-60 seconds"
            },
            "optimization_tips": [
                "Use correct captcha type parameter",
                "Ensure good image quality",
                "Check server load status"
            ],
            "contact": "<EMAIL>"
        }

    else:
        return {
            "issue": "General Captcha Help",
            "description": "General assistance with captcha solving",
            "supported_types": [
                "reCAPTCHA v2 & v3",
                "hCaptcha",
                "FunCaptcha",
                "GeeTest",
                "Cloudflare Turnstile"
            ],
            "contact": "<EMAIL>",
            "documentation": "https://docs.nocaptchaai.com/",
            "api_guide": "https://docs.nocaptchaai.com/api"
        }
