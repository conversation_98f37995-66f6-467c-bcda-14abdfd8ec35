"""
Issue escalation and priority handling functions.
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def escalate_issue(issue: str, urgency: str = "normal") -> Dict[str, Any]:
    """
    Escalate an issue to appropriate team based on urgency.

    Args:
        issue: Description of the issue
        urgency: Urgency level (low, normal, high, critical)

    Returns:
        Dictionary with escalation information
    """
    urgency_lower = urgency.lower()

    if urgency_lower in ["critical", "urgent", "high"]:
        return {
            "escalation_level": "High Priority",
            "contact": "<EMAIL>",
            "description": "Critical issues receive immediate attention",
            "response_time": "Within 1 hour",
            "availability": "24/7 emergency support",
            "phone_support": "Available for enterprise customers",
            "issue_description": issue
        }

    elif urgency_lower == "low":
        return {
            "escalation_level": "Standard Queue",
            "contact": "<EMAIL>",
            "description": "Standard support queue for non-urgent issues",
            "response_time": "Within 48 hours",
            "availability": "Business hours",
            "issue_description": issue
        }

    else:
        return {
            "escalation_level": "Normal Priority",
            "contact": "<EMAIL>",
            "description": "Normal priority support queue",
            "response_time": "Within 24 hours",
            "availability": "Extended business hours",
            "issue_description": issue
        }
