"""
Contact information and routing for customer support.
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def get_support_contact(issue: str) -> Dict[str, Any]:
    """
    Return support contact information based on the issue type.
    
    Args:
        issue: Description of the user's issue
        
    Returns:
        Dictionary with support contact information
    """
    issue_lower = issue.lower()
    
    # NoCaptcha specific support routing
    if any(word in issue_lower for word in ["billing", "payment", "subscription", "plan", "pricing"]):
        return {
            "department": "Billing Support",
            "email": "<EMAIL>",
            "description": "For billing, payment, and subscription issues",
            "hours": "Mon–Fri 9am–6pm UTC",
            "response_time": "Within 24 hours"
        }
    
    elif any(word in issue_lower for word in ["api", "integration", "code", "error", "bug", "technical"]):
        return {
            "department": "Technical Support", 
            "email": "<EMAIL>",
            "description": "For API integration and technical issues",
            "documentation": "https://docs.nocaptchaai.com/",
            "github": "https://github.com/noCaptchaAi/NoCaptcha-Ai-Browser-Extension",
            "hours": "24/7 support available",
            "response_time": "Within 4 hours"
        }
    
    elif any(word in issue_lower for word in ["account", "login", "password", "access", "signup"]):
        return {
            "department": "Account Support",
            "email": "<EMAIL>", 
            "description": "For account access and login issues",
            "hours": "Mon–Fri 8am–8pm UTC",
            "response_time": "Within 12 hours"
        }
    
    elif any(word in issue_lower for word in ["captcha", "solve", "accuracy", "speed", "performance"]):
        return {
            "department": "Service Quality Team",
            "email": "<EMAIL>",
            "description": "For captcha solving accuracy and performance issues", 
            "hours": "24/7 monitoring",
            "response_time": "Within 2 hours"
        }
    
    else:
        return {
            "department": "General Support",
            "email": "<EMAIL>",
            "description": "For general inquiries and other issues",
            "website": "https://nocaptchaai.com/",
            "hours": "Mon–Fri 9am–6pm UTC",
            "response_time": "Within 24 hours"
        }
