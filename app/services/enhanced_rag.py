"""
Enhanced RAG service with confidence scoring and fallback logic.
"""
import logging
from typing import List, Dict, Any, Tuple, Optional
import numpy as np

from app.services.data_ingestion import generate_query_embedding
from app.services.db_vector_store import db_vector_store
from app.config import settings

logger = logging.getLogger(__name__)


class EnhancedRAGService:
    """Enhanced RAG service with confidence scoring."""
    
    def __init__(self, confidence_threshold: float = 0.4):
        """
        Initialize the enhanced RAG service.

        Args:
            confidence_threshold: Minimum confidence score for RAG results (lowered to 0.4)
        """
        self.confidence_threshold = confidence_threshold
        self.min_results = 1
        self.max_results = 5
    
    def search_with_confidence(
        self, 
        query: str, 
        top_k: int = 3
    ) -> Tuple[List[Dict[str, Any]], float, bool]:
        """
        Search documents and return results with confidence assessment.
        
        Args:
            query: User query string
            top_k: Number of top results to return
            
        Returns:
            Tuple of (results, confidence_score, is_confident)
        """
        try:
            # Generate query embedding
            query_embedding = generate_query_embedding(query)
            
            # Search vector store
            results = db_vector_store.search(query_embedding, top_k=top_k)
            
            if not results:
                logger.info("No results found in vector store")
                return [], 0.0, False
            
            # Calculate confidence based on similarity scores
            confidence_score = self._calculate_confidence(results)
            is_confident = confidence_score >= self.confidence_threshold
            
            # Format results
            formatted_results = self._format_results(results)
            
            logger.info(f"RAG search: {len(formatted_results)} results, confidence: {confidence_score:.3f}, confident: {is_confident}")
            
            return formatted_results, confidence_score, is_confident
            
        except Exception as e:
            logger.error(f"Error in RAG search: {e}")
            return [], 0.0, False
    
    def _calculate_confidence(self, results: List[Tuple[Dict[str, Any], float]]) -> float:
        """
        Calculate confidence score based on similarity scores.

        Args:
            results: List of (document, similarity_score) tuples

        Returns:
            Confidence score between 0 and 1
        """
        if not results:
            return 0.0

        # Extract similarity scores
        scores = [score for _, score in results]

        # Use the highest similarity score as base confidence
        max_score = max(scores)

        # More lenient confidence calculation
        # If we have any decent match (>0.3), we should be confident
        if max_score > 0.6:
            return 0.9  # Very confident
        elif max_score > 0.4:
            return 0.7  # Confident
        elif max_score > 0.3:
            return 0.5  # Moderately confident
        else:
            return max_score  # Use raw score for low matches
    
    def _format_results(self, results: List[Tuple[Dict[str, Any], float]]) -> List[Dict[str, Any]]:
        """
        Format search results for response.
        
        Args:
            results: Raw search results
            
        Returns:
            Formatted results list
        """
        formatted = []
        
        for doc, score in results:
            formatted_doc = {
                "title": doc.get("title", "Unknown"),
                "url": doc.get("url", ""),
                "content": doc.get("content", ""),
                "relevance_score": float(score),
                "id": doc.get("id", "")
            }
            formatted.append(formatted_doc)
        
        return formatted
    
    def build_context(self, results: List[Dict[str, Any]], max_length: int = None) -> str:
        """
        Build context string from search results.
        
        Args:
            results: Formatted search results
            max_length: Maximum context length
            
        Returns:
            Context string for LLM
        """
        if max_length is None:
            max_length = settings.MAX_CONTEXT_LENGTH
        
        context_parts = []
        current_length = 0
        
        for result in results:
            title = result.get("title", "Unknown")
            content = result.get("content", "")
            url = result.get("url", "")
            
            # Create document section
            doc_section = f"Document: {title}\n"
            if url:
                doc_section += f"URL: {url}\n"
            doc_section += f"Content: {content}\n\n"
            
            # Check length limit
            if current_length + len(doc_section) > max_length:
                # Try to fit partial content
                remaining = max_length - current_length
                if remaining > 100:  # Only add if meaningful space left
                    partial_content = content[:remaining-50] + "..."
                    doc_section = f"Document: {title}\nContent: {partial_content}\n\n"
                    context_parts.append(doc_section)
                break
            
            context_parts.append(doc_section)
            current_length += len(doc_section)
        
        return "".join(context_parts)
    
    def should_use_rag(self, query: str) -> bool:
        """
        Determine if query is suitable for RAG.

        Args:
            query: User query

        Returns:
            True if query should use RAG
        """
        query_lower = query.lower().strip()

        # Skip RAG for very short queries
        if len(query_lower) < 3:
            return False

        # Skip RAG for simple greetings only
        simple_greetings = ["hi", "hello", "hey", "thanks", "bye"]
        if query_lower in simple_greetings:
            return False

        # ALWAYS try RAG first for these content-related keywords
        content_keywords = [
            "price", "pricing", "cost", "plan", "subscription", "billing",
            "api", "integration", "documentation", "guide", "tutorial",
            "feature", "service", "captcha", "solve", "accuracy", "speed",
            "browser", "extension", "install", "setup", "configure",
            "error", "bug", "issue", "problem", "troubleshoot",
            "how", "what", "when", "where", "why", "which",
            "enterprise", "business", "commercial", "free", "trial"
        ]

        # If query contains content keywords, definitely use RAG
        if any(keyword in query_lower for keyword in content_keywords):
            return True

        # Only skip RAG for pure contact requests without content
        pure_contact_phrases = [
            "contact support", "speak to support", "talk to someone",
            "need help", "get help", "customer service"
        ]

        if any(phrase in query_lower for phrase in pure_contact_phrases):
            return False

        # Default to trying RAG first for everything else
        return True


# Global instance using config settings (60% threshold)
from app.config import settings
enhanced_rag = EnhancedRAGService(confidence_threshold=settings.RAG_CONFIDENCE_THRESHOLD)
