"""
Database-backed vector store for document retrieval.
"""
import faiss
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional

from app.services.embedding_service import EmbeddingService
from app.config import settings

logger = logging.getLogger(__name__)


class DatabaseVectorStore:
    """Vector store that loads embeddings from database."""
    
    def __init__(self):
        self.index = None
        self.documents = []
        self.embeddings = []
        self.dimension = None
        self.is_initialized = False
    
    def load_from_database(self) -> bool:
        """Load embeddings and documents from database."""
        try:
            logger.info("Loading embeddings from database...")
            
            # Load embeddings and documents from database
            embeddings, documents = EmbeddingService.load_all_embeddings()
            
            if not embeddings or not documents:
                logger.warning("No embeddings found in database")
                return False
            
            # Convert embeddings to numpy array
            embeddings_array = np.array(embeddings, dtype=np.float32)
            self.dimension = embeddings_array.shape[1]
            
            # Create FAISS index
            self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embeddings_array)
            
            # Add embeddings to index
            self.index.add(embeddings_array)
            
            # Store documents and embeddings
            self.documents = documents
            self.embeddings = embeddings
            self.is_initialized = True
            
            logger.info(f"Loaded {len(embeddings)} embeddings from database, dimension: {self.dimension}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading embeddings from database: {e}")
            return False
    
    def search(self, query_embedding: List[float], top_k: int = 5) -> List[Tuple[Dict[str, Any], float]]:
        """
        Search for similar documents.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of results to return
            
        Returns:
            List of (document, similarity_score) tuples
        """
        if not self.is_initialized:
            logger.warning("Vector store not initialized")
            return []
        
        try:
            # Convert query to numpy array and normalize
            query_array = np.array([query_embedding], dtype=np.float32)
            faiss.normalize_L2(query_array)
            
            # Search
            similarities, indices = self.index.search(query_array, min(top_k, len(self.documents)))
            
            results = []
            for similarity, idx in zip(similarities[0], indices[0]):
                if idx < len(self.documents):
                    results.append((self.documents[idx], float(similarity)))
            
            return results
            
        except Exception as e:
            logger.error(f"Error during search: {e}")
            return []
    
    def get_context(self, query_embedding: List[float], max_length: int = None) -> str:
        """
        Get context for query by retrieving similar documents.
        
        Args:
            query_embedding: Query embedding vector
            max_length: Maximum context length
            
        Returns:
            Context string
        """
        if max_length is None:
            max_length = settings.MAX_CONTEXT_LENGTH
        
        results = self.search(query_embedding, top_k=settings.TOP_K_RESULTS)
        
        context_parts = []
        current_length = 0
        
        for doc, similarity in results:
            content = doc.get('content', '')
            title = doc.get('title', 'Unknown')
            
            # Add document with title
            doc_text = f"Document: {title}\n{content}\n\n"
            
            if current_length + len(doc_text) > max_length:
                # Truncate if needed
                remaining_length = max_length - current_length
                if remaining_length > 100:  # Only add if we have reasonable space
                    doc_text = doc_text[:remaining_length] + "..."
                    context_parts.append(doc_text)
                break
            
            context_parts.append(doc_text)
            current_length += len(doc_text)
        
        return "".join(context_parts)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics."""
        if not self.is_initialized:
            return {
                'initialized': False,
                'total_documents': 0,
                'dimension': None
            }
        
        return {
            'initialized': True,
            'total_documents': len(self.documents),
            'dimension': self.dimension,
            'index_type': type(self.index).__name__
        }
    
    def is_ready(self) -> bool:
        """Check if vector store is ready for queries."""
        return self.is_initialized and self.index is not None and len(self.documents) > 0


# Global instance
db_vector_store = DatabaseVectorStore()
