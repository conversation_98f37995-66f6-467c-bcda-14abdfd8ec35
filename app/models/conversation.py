"""
Database models for conversations and messages.
"""
import uuid
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Text, DateTime, ForeignKey, Float, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.database.base import Base


class Conversation(Base):
    """Model for storing conversation sessions."""
    
    __tablename__ = "conversations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(String(255), nullable=True, index=True)
    user_id = Column(String(255), nullable=True, index=True)
    title = Column(String(500), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationship to messages
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, session_id={self.session_id}, created_at={self.created_at})>"


class Message(Base):
    """Model for storing individual messages (queries and responses)."""
    
    __tablename__ = "messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), nullable=False)
    message_type = Column(String(50), nullable=False)  # 'query' or 'response'
    content = Column(Text, nullable=False)
    
    # Additional metadata
    sources = Column(JSON, nullable=True)  # Store sources for responses
    relevance_scores = Column(JSON, nullable=True)  # Store relevance scores
    model_used = Column(String(100), nullable=True)  # Which model generated the response
    temperature = Column(Float, nullable=True)  # Temperature used for generation
    max_tokens = Column(Integer, nullable=True)  # Max tokens used
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationship to conversation
    conversation = relationship("Conversation", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, type={self.message_type}, conversation_id={self.conversation_id})>"
