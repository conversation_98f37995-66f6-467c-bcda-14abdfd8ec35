"""
Pydantic schemas for conversation and message models.
"""
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class MessageCreate(BaseModel):
    """Schema for creating a new message."""
    content: str
    message_type: str = Field(..., pattern="^(query|response)$")
    sources: Optional[List[Dict[str, Any]]] = None
    relevance_scores: Optional[List[float]] = None
    model_used: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None


class MessageResponse(BaseModel):
    """Schema for message response."""
    id: uuid.UUID
    conversation_id: uuid.UUID
    message_type: str
    content: str
    sources: Optional[List[Dict[str, Any]]] = None
    relevance_scores: Optional[List[float]] = None
    model_used: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ConversationCreate(BaseModel):
    """Schema for creating a new conversation."""
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    title: Optional[str] = None


class ConversationResponse(BaseModel):
    """Schema for conversation response."""
    id: uuid.UUID
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    title: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    messages: List[MessageResponse] = []

    class Config:
        from_attributes = True


class ChatRequest(BaseModel):
    """Schema for chat request."""
    query: str
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    conversation_id: Optional[uuid.UUID] = None
    max_tokens: Optional[int] = 1000
    temperature: Optional[float] = 0.7


class ChatResponse(BaseModel):
    """Schema for chat response."""
    message_id: uuid.UUID
    conversation_id: uuid.UUID
    response: str
    sources: Optional[List[Dict[str, Any]]] = None
    query_message_id: uuid.UUID
    created_at: datetime


class HealthResponse(BaseModel):
    """Schema for health check response."""
    status: str
    message: str
