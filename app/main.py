"""
FastAPI application for RAG-based customer chatbot.
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging
import os
from typing import Optional
from google import genai

from app.config import settings
from app.services.data_ingestion import fetch_and_process_data, generate_embeddings, generate_query_embedding
from app.services.retrieval import vector_store

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate settings
settings.validate()

# Configure the Gen AI client
client = genai.Client(api_key=settings.GEMINI_API_KEY)

# Create FastAPI app
app = FastAPI(
    title="Customer Chatbot API",
    description="RAG-based customer chatbot using FastAPI and Google Gemini",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ChatRequest(BaseModel):
    query: str
    max_tokens: Optional[int] = 1000
    temperature: Optional[float] = 0.7

class ChatResponse(BaseModel):
    response: str
    sources: Optional[list] = None

class HealthResponse(BaseModel):
    status: str
    message: str

# Global state
app_state = {
    "initialized": False,
    "documents_count": 0
}

@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    try:
        logger.info("Starting application initialization...")
        
        if not os.path.exists(settings.DATA_SOURCES_PATH):
            logger.warning(f"Data sources file not found: {settings.DATA_SOURCES_PATH}")
            return
        
        documents = fetch_and_process_data(settings.DATA_SOURCES_PATH)
        if not documents:
            logger.warning("No documents were loaded")
            return
        
        embeddings = generate_embeddings(documents)
        vector_store.create_index(embeddings, documents)
        
        app_state["initialized"] = True
        app_state["documents_count"] = len(documents)
        
        logger.info(f"Application initialized successfully with {len(documents)} documents")
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")

@app.get("/", response_model=HealthResponse)
async def root():
    return HealthResponse(status="healthy", message="Customer Chatbot API is running")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    if app_state["initialized"]:
        return HealthResponse(
            status="healthy",
            message=f"API is ready with {app_state['documents_count']} documents loaded"
        )
    else:
        return HealthResponse(status="partial", message="API is running but data not initialized")

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    try:
        if not app_state["initialized"]:
            raise HTTPException(status_code=503, detail="Service not ready. Data not initialized.")

        query_embedding = generate_query_embedding(request.query)
        context = vector_store.get_context(query_embedding)

        # Updated prompt - natural conversation without mentioning sources
        prompt = f"""You are a helpful assistant. Answer the user's question naturally and conversationally. Be informative and helpful.

{context}

Question: {request.query}
Answer:"""

        response = client.models.generate_content(
            model=settings.GENERATION_MODEL,
            contents=prompt
        )

        results = vector_store.search(query_embedding, top_k=3)
        sources = [
            {
                "title": doc.get("title", "Unknown"),
                "url": doc.get("url", ""),
                "relevance_score": float(distance)
            }
            for doc, distance in results
        ]

        return ChatResponse(response=response.text, sources=sources)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main_simple:app", host="127.0.0.1", port=8000, reload=True)
