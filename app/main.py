"""
FastAPI application for RAG-based customer chatbot.
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import uuid
from typing import Optional, List
from google import genai
from sqlalchemy.orm import Session

from app.config import settings
from app.database import get_db, create_tables
from app.services.data_ingestion import fetch_and_process_data, generate_embeddings, generate_query_embedding
from app.services.retrieval import vector_store
from app.services.conversation_service import ConversationService
from app.schemas.conversation import (
    ChatRequest,
    ChatResponse,
    HealthResponse,
    ConversationResponse,
    MessageResponse
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate settings
settings.validate()

# Configure the Gen AI client
client = genai.Client(api_key=settings.GEMINI_API_KEY)

# Create FastAPI app
app = FastAPI(
    title="Customer Chatbot API",
    description="RAG-based customer chatbot using FastAPI and Google Gemini",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Remove duplicate Pydantic models - using schemas from app.schemas.conversation

# Global state
app_state = {
    "initialized": False,
    "documents_count": 0
}

@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    try:
        logger.info("Starting application initialization...")

        # Create database tables
        create_tables()
        logger.info("Database tables created/verified")

        if not os.path.exists(settings.DATA_SOURCES_PATH):
            logger.warning(f"Data sources file not found: {settings.DATA_SOURCES_PATH}")
            return

        documents = fetch_and_process_data(settings.DATA_SOURCES_PATH)
        if not documents:
            logger.warning("No documents were loaded")
            return

        embeddings = generate_embeddings(documents)
        vector_store.create_index(embeddings, documents)

        app_state["initialized"] = True
        app_state["documents_count"] = len(documents)

        logger.info(f"Application initialized successfully with {len(documents)} documents")

    except Exception as e:
        logger.error(f"Error during startup: {e}")

@app.get("/", response_model=HealthResponse)
async def root():
    return HealthResponse(status="healthy", message="Customer Chatbot API is running")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    if app_state["initialized"]:
        return HealthResponse(
            status="healthy",
            message=f"API is ready with {app_state['documents_count']} documents loaded"
        )
    else:
        return HealthResponse(status="partial", message="API is running but data not initialized")

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, db: Session = Depends(get_db)):
    try:
        if not app_state["initialized"]:
            raise HTTPException(status_code=503, detail="Service not ready. Data not initialized.")

        # Initialize conversation service
        conversation_service = ConversationService(db)

        # Get or create conversation
        conversation = conversation_service.get_or_create_conversation(
            conversation_id=request.conversation_id,
            session_id=request.session_id,
            user_id=request.user_id
        )

        # Store the user query
        query_message = conversation_service.add_message(
            conversation_id=conversation.id,
            content=request.query,
            message_type="query"
        )

        # Generate response
        query_embedding = generate_query_embedding(request.query)
        context = vector_store.get_context(query_embedding)

        # Updated prompt - natural conversation without mentioning sources
        prompt = f"""You are a helpful assistant. Answer the user's question naturally and conversationally. Be informative and helpful.

{context}

Question: {request.query}
Answer:"""

        response = client.models.generate_content(
            model=settings.GENERATION_MODEL,
            contents=prompt
        )

        results = vector_store.search(query_embedding, top_k=3)
        sources = [
            {
                "title": doc.get("title", "Unknown"),
                "url": doc.get("url", ""),
                "relevance_score": float(distance)
            }
            for doc, distance in results
        ]

        # Store the response
        response_message = conversation_service.add_message(
            conversation_id=conversation.id,
            content=response.text,
            message_type="response",
            sources=sources,
            model_used=settings.GENERATION_MODEL,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )

        # Update conversation title if it's the first query
        if not conversation.title and len(request.query) > 0:
            title = request.query[:50] + "..." if len(request.query) > 50 else request.query
            conversation_service.update_conversation_title(conversation.id, title)

        return ChatResponse(
            message_id=response_message.id,
            conversation_id=conversation.id,
            response=response.text,
            sources=sources,
            query_message_id=query_message.id,
            created_at=response_message.created_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Additional endpoints for conversation management

@app.get("/conversations/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(conversation_id: uuid.UUID, db: Session = Depends(get_db)):
    """Get a specific conversation with its messages."""
    conversation_service = ConversationService(db)
    conversation = conversation_service.get_conversation(conversation_id)

    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    return conversation


@app.get("/conversations", response_model=List[ConversationResponse])
async def get_user_conversations(
    user_id: str,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """Get conversations for a specific user."""
    conversation_service = ConversationService(db)
    conversations = conversation_service.get_user_conversations(user_id, limit)
    return conversations


@app.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    conversation_id: uuid.UUID,
    limit: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Get messages for a specific conversation."""
    conversation_service = ConversationService(db)

    # Verify conversation exists
    conversation = conversation_service.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    messages = conversation_service.get_conversation_messages(conversation_id, limit)
    return messages


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="127.0.0.1", port=8000, reload=True)
