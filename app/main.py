"""
FastAPI application with hybrid RAG + Tool Calling approach.
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from scalar_fastapi import get_scalar_api_reference
from pydantic import BaseModel
import logging
import os
from typing import Optional, List
from google import genai
from google.genai import types

from app.config import settings
from app.services.enhanced_rag import enhanced_rag
from app.services.tool_calling import tool_calling_service
from app.services.function_declarations import ALL_FUNCTION_DECLARATIONS
from app.services.database_service import DatabaseService
from app.services.embedding_service import EmbeddingService
from app.database import create_tables, test_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate configuration
settings.validate()

# Initialize GenAI client
client = genai.Client(api_key=settings.GEMINI_API_KEY)

# Application state
app_state = {
    "initialized": False,
    "documents_count": 0
}

# FastAPI app
app = FastAPI(
    title="NoCaptcha Customer Service Chatbot",
    description="Hybrid RAG + Tool Calling chatbot for customer support",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class SourceItem(BaseModel):
    title: str
    url: str
    relevance_score: Optional[float] = None
    id: Optional[str] = None

class ChatRequest(BaseModel):
    query: str
    conversation_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    message_id: str
    sources: List[SourceItem] = []
    rag_used: bool = False
    confidence_score: Optional[float] = None
    function_called: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    message: str


@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    try:
        logger.info("Starting hybrid RAG + Tool Calling chatbot...")

        # Initialize database
        logger.info("Initializing database...")
        if test_connection():
            create_tables()
            logger.info("Database initialized successfully")
        else:
            logger.warning("Database connection failed - continuing without database")
            app_state["initialized"] = False
            return

        # Load embeddings from database
        logger.info("Loading embeddings from database...")
        from app.services.db_vector_store import db_vector_store
        if db_vector_store.load_from_database():
            stats = EmbeddingService.get_embedding_stats()
            app_state["initialized"] = True
            app_state["documents_count"] = stats['total_documents']
            logger.info(f"Application initialized successfully with {stats['total_documents']} documents from database")
        else:
            logger.warning("No embeddings found in database")
            logger.warning("Please run 'uv run python regenerate_data.py' to generate embeddings")
            app_state["initialized"] = False
            app_state["documents_count"] = 0

    except Exception as e:
        logger.error(f"Error during startup: {e}")
        app_state["initialized"] = False

@app.get("/scalar", include_in_schema=False)
async def scalar_html():
    return get_scalar_api_reference(
        openapi_url=app.openapi_url,
        title=app.title,
    )
    
@app.get("/", response_model=HealthResponse)
async def root():
    return HealthResponse(status="healthy", message="Hybrid RAG + Tool Calling Chatbot API is running")


@app.get("/health", response_model=HealthResponse)
async def health_check():
    if app_state["initialized"]:
        return HealthResponse(
            status="healthy",
            message=f"API is ready with {app_state['documents_count']} documents loaded"
        )
    else:
        return HealthResponse(status="partial", message="API is running but data not initialized")


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Enhanced chat endpoint with RAG + Tool Calling hybrid approach.
    
    Flow:
    1. Try RAG first with confidence scoring
    2. If RAG confidence is low, fall back to function calling
    3. Combine results for coherent response
    """
    try:
        user_query = request.query.strip()
        if not user_query:
            raise HTTPException(status_code=400, detail="Query cannot be empty")

        # Handle conversation ID
        conversation_id = request.conversation_id
        if not conversation_id:
            conversation_id = DatabaseService.create_conversation()
            if not conversation_id:
                logger.warning("Failed to create conversation, continuing without database")
                conversation_id = "temp_conversation"
        
        # Save user message
        user_message_id = DatabaseService.save_user_message(conversation_id, user_query)
        if not user_message_id:
            logger.warning("Failed to save user message")

        # Initialize response variables
        final_response = ""
        sources_list = []
        rag_used = False
        confidence_score = 0.0
        function_called = None

        # Step 1: ALWAYS try RAG first if initialized (be more aggressive)
        if app_state["initialized"] and enhanced_rag.should_use_rag(user_query):
            logger.info("Attempting RAG retrieval...")

            rag_results, confidence_score, is_confident = enhanced_rag.search_with_confidence(
                user_query, top_k=5  # Get more results
            )

            # Use RAG if confidence is 60% or above
            if confidence_score >= 0.6:
                logger.info(f"✅ RAG confidence above 60% (score: {confidence_score:.1%}), using RAG response")

                # Build context from RAG results
                context = enhanced_rag.build_context(rag_results)

                # Use system prompt from config
                prompt = f"""{settings.RAG_SYSTEM_PROMPT}

Documentation:
{context}

User Question: {user_query}

Answer:"""

                # Generate response using RAG
                response = client.models.generate_content(
                    model=settings.GENERATION_MODEL,
                    contents=prompt
                )

                final_response = response.text.strip()
                rag_used = True

                # Format sources
                sources_list = [
                    SourceItem(
                        title=result.get("title", "Unknown"),
                        url=result.get("url", ""),
                        relevance_score=result.get("relevance_score"),
                        id=result.get("id")
                    )
                    for result in rag_results
                ]
            else:
                logger.info(f"❌ RAG confidence below 60% (score: {confidence_score:.1%}), falling back to tool calling")
        else:
            if not app_state["initialized"]:
                logger.info("RAG not initialized, using function calling")
            else:
                logger.info("Query not suitable for RAG, using function calling")

        # Step 2: If RAG wasn't used or wasn't confident, use function calling
        if not rag_used:
            logger.info("Using function calling approach...")
            
            # Create tools configuration
            tools = types.Tool(function_declarations=ALL_FUNCTION_DECLARATIONS)
            generate_config = types.GenerateContentConfig(tools=[tools])
            
            # Create function calling prompt using config
            fc_prompt = f"""{settings.TOOL_CALLING_PROMPT}

                            User Query: "{user_query}"

                            Available functions:
                            - get_support_contact: For getting support contact information
                            - get_pricing_info: For pricing and plan information
                            - get_service_status: For checking service status
                            - get_refund_policy: For refund policy and process information
                            - get_payment_methods: For payment methods and billing information
                            - get_captcha_help: For captcha-related issues and troubleshooting
                            - escalate_issue: For escalating urgent issues

                            Please call the most appropriate function based on the user's query."""

            # Generate response with function calling
            response = client.models.generate_content(
                model=settings.GENERATION_MODEL,
                contents=fc_prompt,
                config=generate_config
            )
            
            part = response.candidates[0].content.parts[0]
            
            if part.function_call:
                # Execute the function call
                fn_name = part.function_call.name
                fn_args = dict(part.function_call.args)
                
                logger.info(f"Executing function: {fn_name} with args: {fn_args}")
                
                # Execute the function
                function_result = tool_calling_service.execute_function(fn_name, fn_args)
                function_called = fn_name
                
                # Format the result into user-friendly response
                final_response = tool_calling_service.format_function_result(function_result, fn_name)
                
            else:
                # Fallback if no function was called
                final_response = "I'd be happy to help! For general support, <NAME_EMAIL> or visit our documentation at https://docs.nocaptchaai.com/"

        # Save bot response to database
        sources_for_db = [source.dict() for source in sources_list] if sources_list else None
        bot_message_id = DatabaseService.save_bot_response(conversation_id, final_response, sources_for_db)
        if not bot_message_id:
            logger.warning("Failed to save bot response")
            bot_message_id = "temp_message"

        return ChatResponse(
            response=final_response,
            conversation_id=conversation_id,
            message_id=bot_message_id,
            sources=sources_list,
            rag_used=rag_used,
            confidence_score=confidence_score if rag_used else None,
            function_called=function_called
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/conversation/{conversation_id}/history")
async def get_conversation_history(conversation_id: str, limit: int = 50):
    """Get conversation history."""
    try:
        if not DatabaseService.conversation_exists(conversation_id):
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        history = DatabaseService.get_conversation_history(conversation_id, limit)
        return {"conversation_id": conversation_id, "messages": history}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving conversation history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host=settings.HOST, port=settings.PORT, reload=settings.DEBUG)
