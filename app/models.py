"""
Database models for the chatbot application.
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, Text, DateTime, ForeignKey, Boolean, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

Base = declarative_base()


class Conversation(Base):
    """Model for storing conversation sessions."""
    
    __tablename__ = "conversations"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    
    # Relationship to messages
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, created_at={self.created_at})>"


class Message(Base):
    """Model for storing individual messages in conversations."""
    
    __tablename__ = "messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String, ForeignKey("conversations.id"), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(String, nullable=False)  # 'user' or 'bot'
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Additional metadata
    sources = Column(Text, nullable=True)  # JSON string of sources used for bot responses
    
    # Relationship to conversation
    conversation = relationship("Conversation", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, type={self.message_type}, conversation_id={self.conversation_id})>"


class Document(Base):
    """Model for storing processed documents."""

    __tablename__ = "documents"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=False)
    url = Column(String, nullable=False, unique=True)
    content = Column(Text, nullable=False)
    content_hash = Column(String, nullable=False)  # Hash of content to detect changes
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship to embedding
    embedding = relationship("DocumentEmbedding", back_populates="document", uselist=False, cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Document(id={self.id}, title={self.title}, url={self.url})>"


class DocumentEmbedding(Base):
    """Model for storing document embeddings."""

    __tablename__ = "document_embeddings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    document_id = Column(String, ForeignKey("documents.id"), nullable=False, unique=True)
    embedding_vector = Column(JSON, nullable=False)  # Store as JSON array
    embedding_model = Column(String, nullable=False)  # Track which model was used
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship to document
    document = relationship("Document", back_populates="embedding")

    def __repr__(self):
        return f"<DocumentEmbedding(id={self.id}, document_id={self.document_id}, model={self.embedding_model})>"
