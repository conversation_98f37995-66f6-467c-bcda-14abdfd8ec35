<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Customer Chatbot API</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #000  100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .chat-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 800px;
      height: 700px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .chat-header {
      background: linear-gradient(135deg, #4f46e5, #000 );
      color: white;
      padding: 24px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .chat-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .chat-header h1 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 8px;
      position: relative;
      z-index: 1;
    }

    .chat-header .subtitle {
      opacity: 0.9;
      font-size: 0.95rem;
      position: relative;
      z-index: 1;
    }

    #chat-box {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
      background: #fafafa;
      position: relative;
    }

    #chat-box::-webkit-scrollbar {
      width: 8px;
    }

    #chat-box::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    #chat-box::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    #chat-box::-webkit-scrollbar-thumb:hover {
      background: #a1a1a1;
    }

    .message {
      margin: 16px 0;
      display: flex;
      align-items: flex-start;
      animation: messageSlide 0.3s ease-out;
    }

    @keyframes messageSlide {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .message-content {
      max-width: 70%;
      padding: 16px 20px;
      border-radius: 18px;
      font-size: 0.95rem;
      line-height: 1.5;
      position: relative;
      word-wrap: break-word;
    }

    .user {
      justify-content: flex-end;
    }

    .user .message-content {
      background: linear-gradient(135deg, #4f46e5, #7c3aed);
      color: white;
      border-bottom-right-radius: 6px;
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }

    .bot {
      justify-content: flex-start;
    }

    .bot .message-content {
      background: white;
      color: #374151;
      border-bottom-left-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      border: 1px solid #e5e7eb;
    }

    .avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.8rem;
      margin: 0 12px;
      flex-shrink: 0;
    }

    .user .avatar {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
      order: 2;
    }

    .bot .avatar {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    .sources {
      margin-top: 12px;
      padding: 16px;
      background: #f8fafc;
      border-radius: 12px;
      border: 1px solid #e2e8f0;
    }

    .sources h4 {
      color: #475569;
      font-size: 0.85rem;
      margin-bottom: 8px;
      font-weight: 600;
    }

    .sources ul {
      list-style: none;
    }

    .sources li {
      margin: 6px 0;
      display: flex;
      align-items: center;
    }

    .sources a {
      color: #4f46e5;
      text-decoration: none;
      font-size: 0.85rem;
      transition: color 0.2s;
      flex: 1;
    }

    .sources a:hover {
      color: #7c3aed;
      text-decoration: underline;
    }

    .relevance-score {
      background: #e0e7ff;
      color: #4f46e5;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 500;
      margin-left: 8px;
    }

    .input-container {
      padding: 24px;
      background: white;
      border-top: 1px solid #e5e7eb;
      display: flex;
      gap: 12px;
      align-items: center;
    }

    #user-input {
      flex: 1;
      padding: 16px 20px;
      border: 2px solid #e5e7eb;
      border-radius: 25px;
      font-size: 0.95rem;
      outline: none;
      transition: all 0.2s;
      background: #fafafa;
    }

    #user-input:focus {
      border-color: #4f46e5;
      background: white;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    #user-input::placeholder {
      color: #9ca3af;
    }

    .send-button {
      background: linear-gradient(135deg, #4f46e5, #7c3aed);
      color: white;
      border: none;
      width: 52px;
      height: 52px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }

    .send-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
    }

    .send-button:active {
      transform: translateY(0);
    }

    .send-button svg {
      width: 20px;
      height: 20px;
    }

    .typing-indicator {
      display: none;
      justify-content: flex-start;
      margin: 16px 0;
      animation: messageSlide 0.3s ease-out;
    }

    .typing-content {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 18px;
      border-bottom-left-radius: 6px;
      padding: 16px 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .typing-dots {
      display: flex;
      gap: 4px;
    }

    .typing-dots span {
      width: 8px;
      height: 8px;
      background: #9ca3af;
      border-radius: 50%;
      animation: typing 1.4s infinite;
    }

    .typing-dots span:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dots span:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typing {
      0%, 60%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      30% {
        transform: scale(1);
        opacity: 1;
      }
    }

    .empty-state {
      text-align: center;
      color: #9ca3af;
      padding: 40px 20px;
      font-style: italic;
    }

    @media (max-width: 768px) {
      body {
        padding: 10px;
      }
      
      .chat-container {
        height: 100vh;
        border-radius: 0;
      }
      
      .chat-header h1 {
        font-size: 1.5rem;
      }
      
      .message-content {
        max-width: 85%;
      }
    }
  </style>
</head>
<body>
  <div class="chat-container">
    <div class="chat-header">
      <h1>🤖 Customer Chatbot API</h1>
    </div>
    
    <div id="chat-box">
      <div class="empty-state">
        👋 Hi there! I'm your AI assistant. Ask me anything to get started.
      </div>
    </div>
    
    <div class="typing-indicator" id="typing-indicator">
      <div class="avatar">🤖</div>
      <div class="typing-content">
        <div class="typing-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
    
    <div class="input-container">
      <input type="text" id="user-input" placeholder="Type your message here..." maxlength="1000">
      <button class="send-button" onclick="sendMessage()">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
        </svg>
      </button>
    </div>
  </div>

  <script>
    let messageCount = 0;

    function showTyping() {
      document.getElementById('typing-indicator').style.display = 'flex';
      const chatBox = document.getElementById('chat-box');
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    function hideTyping() {
      document.getElementById('typing-indicator').style.display = 'none';
    }

    function clearEmptyState() {
      const emptyState = document.querySelector('.empty-state');
      if (emptyState) {
        emptyState.remove();
      }
    }

    async function sendMessage() {
      const inputField = document.getElementById('user-input');
      const chatBox = document.getElementById('chat-box');
      const userMessage = inputField.value.trim();
      
      if (!userMessage) return;

      clearEmptyState();
      
      // Display user's message
      const userDiv = document.createElement('div');
      userDiv.className = 'message user';
      userDiv.innerHTML = `
        <div class="message-content">${escapeHtml(userMessage)}</div>
        <div class="avatar">👤</div>
      `;
      chatBox.appendChild(userDiv);
      
      inputField.value = '';
      inputField.disabled = true;
      showTyping();
      chatBox.scrollTop = chatBox.scrollHeight;

      // Send message to backend
      try {
        const response = await fetch('http://127.0.0.1:8000/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ query: userMessage })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        hideTyping();
        
        // Display bot's response
        const botDiv = document.createElement('div');
        botDiv.className = 'message bot';

        // Convert Markdown response to HTML and sanitize it
        const rawHtml = marked.parse(data.response || '');
        const safeHtml = DOMPurify.sanitize(rawHtml);

        let sourcesHtml = '';
        if (data.sources && data.sources.length > 0) {
          sourcesHtml = `
            <div class="sources">
              <h4>📚 Sources</h4>
              <ul>
                ${data.sources.map(source => `
                  <li>
                    <a href="${escapeHtml(source.url)}" target="_blank" rel="noopener noreferrer">
                      ${escapeHtml(source.title)}
                    </a>
                    <span class="relevance-score">${source.relevance_score ?? ''}</span>
                  </li>
                `).join('')}
              </ul>
            </div>
          `;
        }
        
        botDiv.innerHTML = `
          <div class="avatar">🤖</div>
          <div class="message-content">
            ${safeHtml}
            ${sourcesHtml}
          </div>
        `;
        
        chatBox.appendChild(botDiv);
        chatBox.scrollTop = chatBox.scrollHeight;
        
      } catch (error) {
        console.error('Error:', error);
        hideTyping();
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message bot';
        errorDiv.innerHTML = `
          <div class="avatar">⚠️</div>
          <div class="message-content" style="color: #dc2626;">
            Sorry, I encountered an error while processing your request. Please make sure the FastAPI server is running and try again.
          </div>
        `;
        chatBox.appendChild(errorDiv);
        chatBox.scrollTop = chatBox.scrollHeight;
      }
      
      inputField.disabled = false;
      inputField.focus();
    }

    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    // Enable Enter key to send message
    document.getElementById('user-input').addEventListener('keypress', function(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    });

    // Focus input on page load
    window.addEventListener('load', function() {
      document.getElementById('user-input').focus();
    });
  </script>
  <!-- marked.js (Markdown → HTML) -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <!-- DOMPurify (sanitize the generated HTML) -->
  <script src="https://cdn.jsdelivr.net/npm/dompurify@2.4.3/dist/purify.min.js"></script>
</head>
</body>
</html>
