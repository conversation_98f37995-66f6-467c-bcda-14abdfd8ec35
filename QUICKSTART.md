# Quick Start Guide

## 🚀 Get Started in 4 Steps

### 1. Install Dependencies
```bash
# Make sure you have uv installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install project dependencies
uv sync
```

### 2. Set Up Database
```bash
# Start PostgreSQL with Docker (recommended)
docker-compose up -d postgres

# Initialize database tables
python setup_db.py setup
```

### 3. Set Up Your API Key
```bash
# Copy the environment template
cp .env.example .env

# Edit .env and add your Google Gemini API key
# Get your API key from: https://ai.google.dev/
# Database URL is already configured for Docker setup
```

### 4. Run the Application
```bash
# Option 1: Using the run script
uv run python run.py

# Option 2: Using uvicorn directly
uv run uvicorn app.main:app --reload --host 127.0.0.1 --port 8000

# Option 3: Using the main module
uv run python -m app.main
```

## 🧪 Test the API

### Run Basic Tests
```bash
# Test without API key
uv run python test_basic.py

# Run with pytest
uv run pytest test_basic.py -v
```

### Test with curl
```bash
# Health check
curl http://127.0.0.1:8000/health

# Chat (requires valid API key and data loaded)
curl -X POST "http://127.0.0.1:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"query": "What is FastAPI?", "user_id": "test_user"}'

# Get conversation history
curl "http://127.0.0.1:8000/conversations?user_id=test_user"
```

### Interactive API Documentation
Visit: http://127.0.0.1:8000/docs

## 📝 Configure Data Sources

Edit `data/sources.json` to add your own data sources:

```json
[
  {
    "title": "Your Documentation",
    "link": "https://your-docs.com"
  },
  {
    "title": "FAQ Page", 
    "link": "https://your-site.com/faq"
  }
]
```

## 🔧 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GEMINI_API_KEY` | Google Gemini API key (required) | - |
| `DATABASE_URL` | PostgreSQL connection string | postgresql://chatbot_user:chatbot_password@localhost:5432/chatbot_db |
| `ENVIRONMENT` | Environment (development/production) | development |
| `HOST` | Server host | 127.0.0.1 |
| `PORT` | Server port | 8000 |
| `DEBUG` | Enable debug mode | true |

## 📚 API Endpoints

### Core Endpoints
- `GET /` - Root endpoint
- `GET /health` - Health check
- `POST /chat` - Chat with the bot (now stores conversations)

### Conversation Management
- `GET /conversations/{conversation_id}` - Get specific conversation
- `GET /conversations?user_id={user_id}` - Get user's conversations
- `GET /conversations/{conversation_id}/messages` - Get conversation messages

## 🎯 Next Steps

1. Get your Google Gemini API key from https://ai.google.dev/
2. Set up PostgreSQL database (see DATABASE_SETUP.md for details)
3. Add your data sources to `data/sources.json`
4. Run the application and test it
5. Customize the configuration in `app/config.py`
6. Add more sophisticated data processing in `app/services/`

## 🆘 Troubleshooting

### Database Issues
- **Connection refused**: Make sure PostgreSQL is running (`docker-compose up -d postgres`)
- **Table doesn't exist**: Run `python setup_db.py setup`
- **Permission denied**: Check database credentials in `.env`

### API Issues
- **503 Service Unavailable**: Data not initialized - check your data sources
- **API Key Error**: Make sure your `GEMINI_API_KEY` is set correctly
- **Import Errors**: Run `uv sync` to install dependencies
- **Port in Use**: Change the `PORT` in your `.env` file

### Quick Database Reset
```bash
# Reset everything
python setup_db.py reset
```
