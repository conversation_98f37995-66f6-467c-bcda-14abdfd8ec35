# Quick Start Guide

## 🚀 Get Started in 3 Steps

### 1. Install Dependencies
```bash
# Make sure you have uv installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install project dependencies
uv sync
```

### 2. Set Up Your API Key
```bash
# Copy the environment template
cp .env.example .env

# Edit .env and add your Google Gemini API key
# Get your API key from: https://ai.google.dev/
```

### 3. Run the Application
```bash
# Option 1: Using the run script
uv run python run.py

# Option 2: Using uvicorn directly
uv run uvicorn app.main:app --reload --host 127.0.0.1 --port 8000

# Option 3: Using the main module
uv run python -m app.main
```

## 🧪 Test the API

### Run Basic Tests
```bash
# Test without API key
uv run python test_basic.py

# Run with pytest
uv run pytest test_basic.py -v
```

### Test with curl
```bash
# Health check
curl http://127.0.0.1:8000/health

# Chat (requires valid API key and data loaded)t
curl -X POST "http://127.0.0.1:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"query": "What is FastAPI?"}'
```

### Interactive API Documentation
Visit: http://127.0.0.1:8000/docs

## 📝 Configure Data Sources

Edit `data/sources.json` to add your own data sources:

```json
[
  {
    "title": "Your Documentation",
    "link": "https://your-docs.com"
  },
  {
    "title": "FAQ Page", 
    "link": "https://your-site.com/faq"
  }
]
```

## 🔧 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GEMINI_API_KEY` | Google Gemini API key (required) | - |
| `HOST` | Server host | 127.0.0.1 |
| `PORT` | Server port | 8000 |
| `DEBUG` | Enable debug mode | true |

## 📚 API Endpoints

- `GET /` - Root endpoint
- `GET /health` - Health check
- `POST /chat` - Chat with the bot
- `POST /reload-data` - Reload data sources (admin)

## 🎯 Next Steps

1. Get your Google Gemini API key from https://ai.google.dev/
2. Add your data sources to `data/sources.json`
3. Run the application and test it
4. Customize the configuration in `app/config.py`
5. Add more sophisticated data processing in `app/services/`

## 🆘 Troubleshooting

- **503 Service Unavailable**: Data not initialized - check your data sources
- **API Key Error**: Make sure your `GEMINI_API_KEY` is set correctly
- **Import Errors**: Run `uv sync` to install dependencies
- **Port in Use**: Change the `PORT` in your `.env` file
