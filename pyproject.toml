[project]
name = "customer-chatbot-api"
version = "0.1.1"
description = "RAG-based customer chatbot API using FastAPI and Google Gemini"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "google-genai>=0.3.0",
    "faiss-cpu>=1.7.4",
    "beautifulsoup4>=4.12.0",
    "requests>=2.31.0",
    "python-dotenv>=1.0.0",
    "numpy>=1.24.0",
    "pydantic>=2.5.0",
    "sqlalchemy>=2.0.41",
    "psycopg2-binary>=2.9.10",
    "alembic>=1.16.1",
    "scalar-fastapi>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "httpx>=0.25.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]
