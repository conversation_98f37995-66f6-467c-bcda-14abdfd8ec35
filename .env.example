# Google Gemini API Key (Required)
# Get your API key from: https://ai.google.dev/
GEMINI_API_KEY=your_api_key_here

# Server Configuration (Optional)
HOST=127.0.0.1
PORT=8000
DEBUG=true

# PostgreSQL Database Configuration (Required for message storage)
DATABASE_URL=postgresql://username:password@localhost:5432/chatbot_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=chatbot_db
DB_USER=username
DB_PASSWORD=password

# You can copy this file to .env and fill in your actual values
# cp .env.example .env
