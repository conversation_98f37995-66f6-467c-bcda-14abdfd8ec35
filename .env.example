# Google Gemini API Key (Required)
# Get your API key from: https://ai.google.dev/
GEMINI_API_KEY=your_api_key_here

# Database Configuration (Required)
# PostgreSQL connection string
DATABASE_URL=postgresql://chatbot_user:chatbot_password@localhost:5432/chatbot_db

# Environment Configuration
ENVIRONMENT=development

# Server Configuration (Optional)
HOST=127.0.0.1
PORT=8000
DEBUG=true

# You can copy this file to .env and fill in your actual values
# cp .env.example .env

# For production, use a secure database URL like:
# DATABASE_URL=************************************************/your-db-name
