#!/usr/bin/env python3
"""
Test script for database functionality.
"""
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.database.connection import SessionLocal
from app.services.conversation_service import ConversationService
from app.schemas.conversation import ConversationCreate

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_database_operations():
    """Test basic database operations."""
    try:
        logger.info("Testing database operations...")
        
        # Create database session
        db = SessionLocal()
        conversation_service = ConversationService(db)
        
        # Test 1: Create a conversation
        logger.info("Test 1: Creating a conversation...")
        conversation_data = ConversationCreate(
            session_id="test_session_123",
            user_id="test_user_456",
            title="Test Conversation"
        )
        conversation = conversation_service.create_conversation(conversation_data)
        logger.info(f"✅ Created conversation: {conversation.id}")
        
        # Test 2: Add a query message
        logger.info("Test 2: Adding a query message...")
        query_message = conversation_service.add_message(
            conversation_id=conversation.id,
            content="What is FastAPI?",
            message_type="query"
        )
        logger.info(f"✅ Added query message: {query_message.id}")
        
        # Test 3: Add a response message
        logger.info("Test 3: Adding a response message...")
        response_message = conversation_service.add_message(
            conversation_id=conversation.id,
            content="FastAPI is a modern, fast web framework for building APIs with Python.",
            message_type="response",
            sources=[{"title": "FastAPI Docs", "url": "https://fastapi.tiangolo.com/"}],
            model_used="gemini-2.0-flash-exp",
            temperature=0.7,
            max_tokens=1000
        )
        logger.info(f"✅ Added response message: {response_message.id}")
        
        # Test 4: Retrieve conversation messages
        logger.info("Test 4: Retrieving conversation messages...")
        messages = conversation_service.get_conversation_messages(conversation.id)
        logger.info(f"✅ Retrieved {len(messages)} messages")
        
        for msg in messages:
            logger.info(f"  - {msg.message_type}: {msg.content[:50]}...")
        
        # Test 5: Get user conversations
        logger.info("Test 5: Getting user conversations...")
        user_conversations = conversation_service.get_user_conversations("test_user_456")
        logger.info(f"✅ Found {len(user_conversations)} conversations for user")
        
        # Test 6: Update conversation title
        logger.info("Test 6: Updating conversation title...")
        updated_conversation = conversation_service.update_conversation_title(
            conversation.id,
            "Updated Test Conversation"
        )
        logger.info(f"✅ Updated conversation title: {updated_conversation.title}")
        
        logger.info("🎉 All database tests passed!")
        
    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        raise
    finally:
        db.close()


def test_database_connection():
    """Test database connection."""
    try:
        logger.info("Testing database connection...")
        
        from app.database.connection import engine
        
        with engine.connect() as conn:
            from sqlalchemy import text
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            if row and row[0] == 1:
                logger.info("✅ Database connection successful!")
            else:
                raise Exception("Unexpected result from test query")
                
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test database functionality")
    parser.add_argument(
        "test_type",
        choices=["connection", "operations", "all"],
        default="all",
        nargs="?",
        help="Type of test to run"
    )
    
    args = parser.parse_args()
    
    try:
        if args.test_type in ["connection", "all"]:
            test_database_connection()
        
        if args.test_type in ["operations", "all"]:
            test_database_operations()
            
        logger.info("🎉 All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Tests failed: {e}")
        sys.exit(1)
