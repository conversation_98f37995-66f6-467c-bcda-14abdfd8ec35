# NoCaptcha AI Customer Service Chatbot

A sophisticated hybrid RAG + Tool Calling customer service chatbot built with FastAPI, Google Gemini, and PostgreSQL.

## 🚀 Features

### **Hybrid Intelligence System**
- **🧠 RAG (Retrieval-Augmented Generation)**: Answers questions from indexed documentation
- **🛠️ Tool Calling**: Handles support requests, refunds, payments, and captcha issues
- **🎯 Smart Routing**: Automatically chooses the best approach based on query type and confidence

### **Advanced Capabilities**
- **📊 Confidence Scoring**: Intelligent decision-making with configurable thresholds
- **💾 PostgreSQL Integration**: Persistent storage for embeddings, conversations, and messages
- **🔄 Real-time Chat**: FastAPI-based REST API with conversation tracking
- **🎨 Modern Web Interface**: Interactive HTML interface with response type indicators
- **⚙️ Configurable**: Environment-based configuration for all settings

### **Customer Support Functions**
- **📞 Support Contact Routing**: Intelligent routing to appropriate support teams
- **💰 Pricing Information**: Detailed pricing and plan information
- **🔄 Refund Policy**: Complete refund process and policy details
- **💳 Payment Methods**: Payment options and billing information
- **🤖 Captcha Help**: Troubleshooting for captcha-related issues
- **🚨 Issue Escalation**: Priority-based issue escalation system

## 🏗️ Architecture

### **Hybrid Decision Flow**
```
User Query → Query Analysis → RAG Search → Confidence Check (60% Threshold)
                                ↓
                    >= 60% Confidence: RAG Response
                    < 60% Confidence: Tool Calling → Function Execution
```

### **Response Types**
- **📚 RAG Responses**: From indexed documentation with confidence scores
- **🛠️ Tool Responses**: Structured information from support functions
- **💬 Fallback**: Generic responses when neither approach is suitable

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- PostgreSQL database
- Google Gemini API key
- uv package manager

### Installation

1. **Clone and Setup**:
```bash
git clone <repository-url>
cd customer-chatbot-api
uv sync
```

2. **Environment Configuration**:
```bash
cp .env.example .env
# Edit .env with your settings (see Configuration section)
```

3. **Database Setup**:
```bash
# Initialize database and generate embeddings
uv run python regenerate_data.py
```

4. **Start Server**:
```bash
uv run python app/main.py
```

5. **Access Interface**:
```
http://127.0.0.1:8000
```

## ⚙️ Configuration

### **Environment Variables**

#### **API Configuration**
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

#### **Model Configuration**
```env
EMBEDDING_MODEL=models/text-embedding-004
GENERATION_MODEL=models/gemini-2.0-flash-exp
```

#### **RAG Configuration**
```env
RAG_CONFIDENCE_THRESHOLD=0.6    # 60% threshold - RAG if >= 60%, Tools if < 60%
RAG_FALLBACK_THRESHOLD=0.6      # Same as main threshold
TOP_K_RESULTS=5
MAX_CONTEXT_LENGTH=4000
```

#### **Database Configuration**
```env
DATABASE_URL=postgresql://chatbot_user:chatbot_secure_2024@localhost:5432/chatbot_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=chatbot_db
DB_USER=chatbot_user
DB_PASSWORD=chatbot_secure_2024
```

#### **Company Information**
```env
COMPANY_NAME=NoCaptcha AI
COMPANY_DOMAIN=nocaptchaai.com
SUPPORT_EMAIL=<EMAIL>
```

## 📡 API Endpoints

### **Chat Endpoint**
```http
POST /chat
Content-Type: application/json

{
  "query": "What are your pricing plans?",
  "conversation_id": "optional-uuid"
}
```

**Response:**
```json
{
  "response": "Here are our pricing plans...",
  "conversation_id": "uuid",
  "message_id": "uuid",
  "sources": [...],
  "rag_used": true,
  "confidence_score": 0.85,
  "function_called": null
}
```

### **Other Endpoints**
- `GET /health`: System health check
- `GET /conversation/{id}/history`: Conversation history

## 🗄️ Database Schema

### **Tables**
1. **`conversations`**: Chat sessions with unique IDs
2. **`messages`**: Individual messages with type (user/bot)
3. **`documents`**: Processed documents with content hashes
4. **`document_embeddings`**: 768-dimensional vector embeddings

## 🛠️ Available Tools

### **Support Functions**
- `get_support_contact(issue)`: Route to appropriate support team
- `get_pricing_info(service_type)`: Get pricing information
- `get_service_status()`: Check system status
- `get_refund_policy()`: Refund policy and process
- `get_payment_methods()`: Payment options and billing
- `get_captcha_help(issue_type)`: Captcha troubleshooting
- `escalate_issue(issue, urgency)`: Priority escalation

## 🎯 Usage Examples

### **RAG Queries** (Answered from documentation)
```
"What are your pricing plans?" → RAG Response with specific pricing
"How do I integrate the API?" → RAG Response with integration guide
"What captcha types are supported?" → RAG Response with supported types
```

### **Tool Calling Queries** (Handled by functions)
```
"I need a refund" → get_refund_policy() → Refund process details
"Payment methods?" → get_payment_methods() → Payment options
"Captcha accuracy issues" → get_captcha_help() → Troubleshooting guide
```

## 🔧 Development

### **Adding New Data Sources**
1. Add URLs to `data/sources.json`
2. Run `uv run python regenerate_data.py`
3. Restart server to load new embeddings

### **Adding New Tools**
1. Add function to `app/services/support_utils.py`
2. Add declaration to `app/services/function_declarations.py`
3. Update `app/services/tool_calling.py` with execution and formatting

## 📊 Performance

### **Startup Time**
- **Fast**: ~5-10 seconds (loads from database)
- **No API calls**: Embeddings pre-generated and stored

### **Response Types**
- **RAG**: ~2-5 seconds (embedding search + generation)
- **Tool Calling**: ~1-3 seconds (function execution + formatting)

## 🎨 Web Interface Features

### **Visual Indicators**
- **🟢 RAG Badge**: Shows confidence percentage
- **🔵 Tool Badge**: Shows function name
- **⚫ Fallback Badge**: Generic responses

### **Response Details**
- Source attribution for RAG responses
- Relevance scores for documents
- Function execution tracking

## 📝 License

MIT License - see LICENSE file for details.
