# Customer Chatbot API

A RAG (Retrieval-Augmented Generation) based customer chatbot API built with FastAPI and Google Gemini. This application fetches data from various sources, creates embeddings, provides intelligent responses to user queries, and stores all conversations in a PostgreSQL database with unique message IDs.

## ✨ Features

- **RAG-based responses** using Google Gemini and FAISS vector search
- **PostgreSQL database** for persistent conversation storage
- **Unique message IDs** for tracking queries and responses
- **Conversation management** with session and user tracking
- **Source attribution** for generated responses
- **RESTful API** with comprehensive endpoints
- **Docker support** for easy database setup

## 🧰 Tools & Libraries

- **FastAPI**: For building the API
- **Google Gen AI SDK**: To access Google's Gemini models
- **FAISS**: For vector similarity search
- **PostgreSQL**: For conversation and message storage
- **SQLAlchemy**: For database ORM
- **Alembic**: For database migrations
- **BeautifulSoup**: For web scraping
- **UV**: Modern Python package manager

## 📁 Project Structure

```
customer-chatbot-api/
├── app/
│   ├── __init__.py
│   ├── main.py               # FastAPI application
│   ├── config.py             # Configuration settings
│   ├── database/             # Database configuration
│   │   ├── __init__.py
│   │   ├── base.py           # SQLAlchemy base
│   │   └── connection.py     # Database connection
│   ├── models/               # Database models
│   │   ├── __init__.py
│   │   └── conversation.py   # Conversation and message models
│   ├── schemas/              # Pydantic schemas
│   │   ├── __init__.py
│   │   └── conversation.py   # API request/response schemas
│   └── services/
│       ├── __init__.py
│       ├── conversation_service.py  # Conversation management
│       ├── data_ingestion.py        # Data fetching and processing
│       └── retrieval.py             # Retrieval logic
├── data/
│   └── sources.json          # JSON file with data sources
├── migrations/               # Database migrations
│   ├── env.py               # Alembic environment
│   └── script.py.mako       # Migration template
├── docker-compose.yml        # PostgreSQL setup
├── alembic.ini              # Alembic configuration
├── setup_db.py              # Database setup script
├── pyproject.toml           # Project dependencies and configuration
├── .env.example             # Environment variables template
├── DATABASE_SETUP.md        # Database setup guide
└── README.md                # Project documentation
```

## 🛠️ Setup Instructions

### 1. Install Dependencies

This project uses `uv` as the package manager. If you don't have `uv` installed:

```bash
# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh
```

Then install the project dependencies:

```bash
# Install all dependencies
uv sync
```

### 2. Set Up API Key

1. Obtain your API key from [Google AI Studio](https://ai.google.dev/)
2. Copy the environment template:
   ```bash
   cp .env.example .env
   ```
3. Edit `.env` and add your API key:
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```

### 3. Configure Data Sources

Edit `data/sources.json` to include your data sources:

```json
[
  {
    "title": "Your Documentation",
    "link": "https://your-docs.com"
  },
  {
    "title": "FAQ Page",
    "link": "https://your-site.com/faq"
  }
]
```

## 🚀 Running the Application

### Development Mode

```bash
# Run with auto-reload
uv run uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

### Production Mode

```bash
# Run in production
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Using the built-in runner

```bash
# Run using the main module
uv run python -m app.main
```

## 📡 API Endpoints

### Health Check
```http
GET /
GET /health
```

### Chat Endpoint
```http
POST /chat
Content-Type: application/json

{
  "query": "What is the purpose of this project?",
  "max_tokens": 1000,
  "temperature": 0.7
}
```

Response:
```json
{
  "response": "This project is a RAG-based customer chatbot...",
  "sources": [
    {
      "title": "Documentation",
      "url": "https://example.com",
      "relevance_score": 0.85
    }
  ]
}
```

### Reload Data (Admin)
```http
POST /reload-data
```

## 🧪 Testing the API

### Using curl

```bash
# Health check
curl http://127.0.0.1:8000/health

# Chat request
curl -X POST "http://127.0.0.1:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"query": "How do I use this API?"}'
```

### Using the Interactive Docs

Visit `http://127.0.0.1:8000/docs` for the automatic interactive API documentation.

## 🔧 Configuration

The application can be configured through environment variables:

- `GEMINI_API_KEY`: Your Google Gemini API key (required)
- `HOST`: Server host (default: 127.0.0.1)
- `PORT`: Server port (default: 8000)
- `DEBUG`: Enable debug mode (default: false)

## 📝 Development

### Code Formatting

```bash
# Format code
uv run black .
uv run isort .

# Lint code
uv run flake8 .
```

### Running Tests

```bash
# Run tests
uv run pytest
```

## 🚨 Important Notes

- **Model Selection**: The application uses `gemini-2.0-flash-exp` for generation and `models/embedding-001` for embeddings
- **Environment Variables**: Always keep your API keys secure using environment variables
- **Error Handling**: The application includes comprehensive error handling for network requests and API responses
- **Rate Limiting**: Be mindful of API rate limits when processing large amounts of data

## 🔒 Security Considerations

- Never commit your `.env` file or API keys to version control
- Use environment variables for all sensitive configuration
- Consider implementing authentication for production deployments
- Configure CORS appropriately for your use case

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Google AI Documentation](https://ai.google.dev/docs)
- [FAISS Documentation](https://github.com/facebookresearch/faiss/wiki)
- [UV Documentation](https://docs.astral.sh/uv/)

